class CreateHealthChecks < ActiveRecord::Migration[8.0]
  def change
    create_table :health_checks do |t|
      t.references :site, null: false, foreign_key: true
      t.string :status, null: false, default: 'unknown'
      t.float :response_time, default: 0.0
      t.integer :jobs_done, default: 0
      t.integer :jobs_pending, default: 0
      t.integer :jobs_failed, default: 0
      t.integer :jobs_queued, default: 0
      t.datetime :last_sync_at
      t.integer :visitor_count, default: 0

      t.timestamps
    end

    add_index :health_checks, :status
    add_index :health_checks, :created_at
  end
end
