class CreateAlerts < ActiveRecord::Migration[8.0]
  def change
    create_table :alerts do |t|
      t.references :site, null: false, foreign_key: true
      t.string :alert_type, null: false
      t.string :email, null: false
      t.boolean :is_active, default: true
      t.datetime :last_sent_at

      t.timestamps
    end

    add_index :alerts, [ :site_id, :alert_type ]
    add_index :alerts, :is_active
  end
end
