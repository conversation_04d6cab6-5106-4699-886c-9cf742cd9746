class CreateSites < ActiveRecord::Migration[8.0]
  def change
    create_table :sites do |t|
      t.string :name, null: false
      t.string :ip_address, null: false
      t.integer :port, null: false
      t.string :status, default: 'unknown'
      t.datetime :last_check_at
      t.float :response_time

      t.timestamps
    end

    add_index :sites, [ :ip_address, :port ], unique: true
    add_index :sites, :status
  end
end
