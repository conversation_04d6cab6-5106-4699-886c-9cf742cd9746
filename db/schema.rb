# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_15_081346) do
  create_table "alerts", force: :cascade do |t|
    t.integer "site_id", null: false
    t.string "alert_type", null: false
    t.string "email", null: false
    t.boolean "is_active", default: true
    t.datetime "last_sent_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["is_active"], name: "index_alerts_on_is_active"
    t.index ["site_id", "alert_type"], name: "index_alerts_on_site_id_and_alert_type"
    t.index ["site_id"], name: "index_alerts_on_site_id"
  end

  create_table "health_checks", force: :cascade do |t|
    t.integer "site_id", null: false
    t.string "status", default: "unknown", null: false
    t.float "response_time", default: 0.0
    t.integer "jobs_done", default: 0
    t.integer "jobs_pending", default: 0
    t.integer "jobs_failed", default: 0
    t.integer "jobs_queued", default: 0
    t.datetime "last_sync_at"
    t.integer "visitor_count", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_health_checks_on_created_at"
    t.index ["site_id"], name: "index_health_checks_on_site_id"
    t.index ["status"], name: "index_health_checks_on_status"
  end

  create_table "sites", force: :cascade do |t|
    t.string "name", null: false
    t.string "ip_address", null: false
    t.integer "port", null: false
    t.string "status", default: "unknown"
    t.datetime "last_check_at"
    t.float "response_time"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ip_address", "port"], name: "index_sites_on_ip_address_and_port", unique: true
    t.index ["status"], name: "index_sites_on_status"
  end

  add_foreign_key "alerts", "sites"
  add_foreign_key "health_checks", "sites"
end
