# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

# Create sample sites for testing
puts "Creating sample sites..."

# Clear existing data
Site.destroy_all

# Create sites with different statuses
sites = [
  { name: 'Production App', ip_address: '*************', port: 3000, status: 'up' },
  { name: 'Staging Server', ip_address: '*************', port: 3000, status: 'up' },
  { name: 'Development Server', ip_address: '*************', port: 3000, status: 'down' },
  { name: 'QA Environment', ip_address: '*************', port: 3000, status: 'unknown' },
  { name: 'Legacy System', ip_address: '*************', port: 8080, status: 'up' }
]

created_sites = sites.map do |site_data|
  Site.create!(
    name: site_data[:name],
    ip_address: site_data[:ip_address],
    port: site_data[:port],
    status: site_data[:status],
    last_check_at: Time.current - rand(1..60).minutes,
    response_time: site_data[:status] == 'up' ? rand(50..500) : nil
  )
end

puts "Created #{created_sites.size} sites"

# Create health checks for each site
puts "Creating sample health checks..."

created_sites.each do |site|
  # Create between 5-10 health checks for each site
  rand(5..10).times do |i|
    # Determine if this health check should be up or down
    status = if site.status == 'unknown'
               [ 'up', 'down' ].sample
    else
               # Mostly match the site status, but occasionally differ
               rand < 0.8 ? site.status : (site.status == 'up' ? 'down' : 'up')
    end

    # Create health check with appropriate data
    health_check = site.health_checks.create!(
      status: status,
      response_time: status == 'up' ? rand(50..500) : rand(1000..5000),
      jobs_done: status == 'up' ? rand(10..100) : 0,
      jobs_pending: rand(0..20),
      jobs_failed: status == 'down' ? rand(5..20) : rand(0..5),
      jobs_queued: rand(0..30),
      last_sync_at: status == 'up' ? Time.current - rand(1..120).minutes : nil,
      visitor_count: status == 'up' ? rand(10..1000) : 0,
      created_at: Time.current - rand(1..24).hours
    )
  end
end

puts "Created #{HealthCheck.count} health checks"

# Create sample alerts
puts "Creating sample alerts..."

alert_types = [ 'downtime', 'performance' ]
emails = [ '<EMAIL>', '<EMAIL>', '<EMAIL>' ]

created_sites.each do |site|
  # Create 1-2 alerts for each site
  rand(1..2).times do
    site.alerts.create!(
      alert_type: alert_types.sample,
      email: emails.sample,
      is_active: [ true, true, false ].sample, # 2/3 chance of being active
      last_sent_at: rand < 0.5 ? Time.current - rand(1..48).hours : nil
    )
  end
end

puts "Created #{Alert.count} alerts"

puts "Seed data creation complete!"
