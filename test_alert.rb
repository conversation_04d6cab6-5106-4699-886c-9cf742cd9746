# Test script to manually trigger an alert
# Run with: rails runner test_alert.rb

puts "Testing alert system..."

# Find a site with alerts
site = Site.first
if site.nil?
  puts "No sites found. Please run 'rails db:seed' first."
  exit
end

# Find or create an alert for this site
alert = site.alerts.first
if alert.nil?
  alert = site.alerts.create!(
    alert_type: 'downtime',
    email: '<EMAIL>',
    is_active: true
  )
  puts "Created test alert for #{site.name}"
end

puts "Site: #{site.name}"
puts "Alert email: #{alert.email}"
puts "Alert type: #{alert.alert_type}"

# Test downtime alert
puts "\nSending downtime alert..."
AlertService.new(alert).send_downtime_alert("Connection timeout")

puts "Alert sent! Check tmp/mail directory for the email file."

# Create a health check to test performance alert
health_check = site.health_checks.create!(
  status: 'up',
  response_time: 6000, # Over 5000ms threshold
  jobs_done: 50,
  jobs_pending: 10,
  jobs_failed: 2,
  jobs_queued: 5,
  last_sync_at: Time.current,
  visitor_count: 100
)

# Create a performance alert if it doesn't exist
perf_alert = site.alerts.find_by(alert_type: 'performance')
if perf_alert.nil?
  perf_alert = site.alerts.create!(
    alert_type: 'performance',
    email: '<EMAIL>',
    is_active: true
  )
end

puts "\nSending performance alert..."
AlertService.new(perf_alert).send_performance_alert(health_check)

puts "Performance alert sent! Check tmp/mail directory for the email files."
puts "\nTest completed successfully!"
