(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[484],{5041:(e,t,s)=>{"use strict";s.d(t,{n:()=>d});var a=s(12115),i=s(34560),r=s(7165),n=s(25910),l=s(52020),o=class extends n.Q{#e;#t=void 0;#s;#a;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,l.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,l.EN)(t.mutationKey)!==(0,l.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#r(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#i(),this.#r()}mutate(e,t){return this.#a=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#i(){let e=this.#s?.state??(0,i.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#r(e){r.jG.batch(()=>{if(this.#a&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#a.onSuccess?.(e.data,t,s),this.#a.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#a.onError?.(e.error,t,s),this.#a.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},c=s(26715);function d(e,t){let s=(0,c.jE)(t),[i]=a.useState(()=>new o(s,e));a.useEffect(()=>{i.setOptions(e)},[i,e]);let n=a.useSyncExternalStore(a.useCallback(e=>i.subscribe(r.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),d=a.useCallback((e,t)=>{i.mutate(e,t).catch(l.lQ)},[i]);if(n.error&&(0,l.GU)(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:d,mutateAsync:n.mutate}}},6846:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(95155),i=s(12115),r=s(26715),n=s(32960),l=s(5041),o=s(25731),c=s(47080),d=s(23861),u=s(84616),h=s(1243),m=s(13717),x=s(62525);function p(){let[e,t]=(0,i.useState)(null),[s,p]=(0,i.useState)(!1),[y,b]=(0,i.useState)(null),g=(0,r.jE)(),{data:f=[],isLoading:v}=(0,n.I)({queryKey:["sites"],queryFn:o.LB.getAll}),{data:j=[],isLoading:w}=(0,n.I)({queryKey:["alerts",null==e?void 0:e.id],queryFn:()=>e?o.Ao.getBySiteId(e.id):Promise.resolve([]),enabled:!!e}),N=(0,l.n)({mutationFn:e=>o.Ao.create(e.siteId,e.alert),onSuccess:()=>{g.invalidateQueries({queryKey:["alerts",null==e?void 0:e.id]}),p(!1)}}),A=(0,l.n)({mutationFn:e=>{let{id:t,...s}=e;return o.Ao.update(t,s)},onSuccess:()=>{g.invalidateQueries({queryKey:["alerts",null==e?void 0:e.id]}),b(null)}}),S=(0,l.n)({mutationFn:o.Ao.delete,onSuccess:()=>{g.invalidateQueries({queryKey:["alerts",null==e?void 0:e.id]})}});return(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Alerts"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Manage email alerts for your monitored sites"})]})}),(0,a.jsx)("div",{className:"bg-white shadow sm:rounded-lg",children:(0,a.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"Select a Site"}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:f.map(s=>(0,a.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer text-black ".concat((null==e?void 0:e.id)===s.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-blue-300"),onClick:()=>t(s),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-blue-500 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:s.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[s.ip_address,":",s.port]})]})]})},s.id))})]})}),e&&(0,a.jsx)("div",{className:"bg-white shadow sm:rounded-lg",children:(0,a.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:["Alerts for ",e.name]}),(0,a.jsxs)("button",{onClick:()=>p(!0),className:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"Add Alert"]})]}),(s||y)&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md mb-4",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:y?"Edit Alert":"Add New Alert"}),(0,a.jsxs)("form",{onSubmit:t=>{t.preventDefault();let s=new FormData(t.currentTarget),a={alert_type:s.get("alert_type"),email:s.get("email"),is_active:"on"===s.get("is_active"),last_sent_at:null};y?A.mutate({id:y.id,...a}):e&&N.mutate({siteId:e.id,alert:a})},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"alert_type",className:"block text-sm font-medium text-gray-700",children:"Alert Type"}),(0,a.jsxs)("select",{id:"alert_type",name:"alert_type",defaultValue:(null==y?void 0:y.alert_type)||"downtime",className:"mt-1 block w-full pl-3 pr-10 py-2 shadow-sm text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black p-2 rounded-md",children:[(0,a.jsx)("option",{value:"downtime",children:"Downtime Alert"}),(0,a.jsx)("option",{value:"performance",children:"Performance Alert"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,a.jsx)("input",{type:"email",name:"email",id:"email",required:!0,defaultValue:(null==y?void 0:y.email)||"",className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black p-2",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"is_active",name:"is_active",type:"checkbox",defaultChecked:!y||y.is_active,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"is_active",className:"ml-2 block text-sm text-gray-900",children:"Active"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{p(!1),b(null)},className:"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:N.isPending||A.isPending,className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:N.isPending||A.isPending?"Saving...":"Save"})]})]})]}),v||w?(0,a.jsx)("div",{className:"flex justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):j.length>0?(0,a.jsx)("ul",{className:"divide-y divide-gray-200",children:j.map(e=>(0,a.jsx)("li",{className:"py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 rounded-full ".concat("downtime"===e.alert_type?"bg-red-100":"bg-yellow-100"),children:(0,a.jsx)(h.A,{className:"h-5 w-5 ".concat("downtime"===e.alert_type?"text-red-500":"text-yellow-500")})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"downtime"===e.alert_type?"Downtime Alert":"Performance Alert"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.email})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.is_active?"Active":"Inactive"}),(0,a.jsx)("button",{onClick:()=>b(e),className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{confirm("Are you sure you want to delete this alert?")&&S.mutate(e.id)},className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]})]})},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No alerts"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by adding an alert for this site."})]})]})})]})})}},13717:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},22325:(e,t,s)=>{Promise.resolve().then(s.bind(s,6846))},25731:(e,t,s)=>{"use strict";s.d(t,{Ao:()=>n,LB:()=>i,yD:()=>r});let a=s(23464).A.create({baseURL:"http://localhost:3000/api",headers:{"Content-Type":"application/json"}}),i={getAll:async()=>(await a.get("/sites")).data,getById:async e=>(await a.get("/sites/".concat(e))).data,create:async e=>(await a.post("/sites",{site:e})).data,update:async(e,t)=>(await a.put("/sites/".concat(e),{site:t})).data,delete:async e=>{await a.delete("/sites/".concat(e))},getFailedJobs:async e=>(await a.get("/sites/".concat(e,"/failed_jobs"))).data},r={getBySiteId:async(e,t,s)=>{let i=new URLSearchParams;t&&i.append("start_date",t.toISOString()),s&&i.append("end_date",s.toISOString());let r="/sites/".concat(e,"/health_checks").concat(i.toString()?"?".concat(i.toString()):"");return(await a.get(r)).data},getById:async e=>(await a.get("/health_checks/".concat(e))).data,create:async(e,t)=>(await a.post("/sites/".concat(e,"/health_checks"),{health_check:t})).data,getAllWithDateRange:async(e,t)=>{let s=new URLSearchParams;e&&s.append("start_date",e.toISOString()),t&&s.append("end_date",t.toISOString());let i="/health_checks".concat(s.toString()?"?".concat(s.toString()):"");return(await a.get(i)).data}},n={getBySiteId:async e=>(await a.get("/sites/".concat(e,"/alerts"))).data,create:async(e,t)=>(await a.post("/sites/".concat(e,"/alerts"),{alert:t})).data,update:async(e,t)=>(await a.put("/alerts/".concat(e),{alert:t})).data,delete:async e=>{await a.delete("/alerts/".concat(e))}}},34560:(e,t,s)=>{"use strict";s.d(t,{$:()=>l,s:()=>n});var a=s(7165),i=s(57948),r=s(6784),n=class extends i.k{#n;#l;#o;constructor(e){super(),this.mutationId=e.mutationId,this.#l=e.mutationCache,this.#n=[],this.state=e.state||l(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#n.includes(e)||(this.#n.push(e),this.clearGcTimeout(),this.#l.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#n=this.#n.filter(t=>t!==e),this.scheduleGc(),this.#l.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#n.length||("pending"===this.state.status?this.scheduleGc():this.#l.remove(this))}continue(){return this.#o?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#c({type:"continue"})};this.#o=(0,r.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#c({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#c({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#l.canRun(this)});let s="pending"===this.state.status,a=!this.#o.canStart();try{if(s)t();else{this.#c({type:"pending",variables:e,isPaused:a}),await this.#l.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#c({type:"pending",context:t,variables:e,isPaused:a})}let i=await this.#o.start();return await this.#l.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#l.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#c({type:"success",data:i}),i}catch(t){try{throw await this.#l.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#l.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#c({type:"error",error:t})}}finally{this.#l.runNext(this)}}#c(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),a.jG.batch(()=>{this.#n.forEach(t=>{t.onMutationUpdate(e)}),this.#l.notify({mutation:this,type:"updated",action:e})})}};function l(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},47080:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var a=s(95155),i=s(6874),r=s.n(i),n=s(35695),l=s(57340),o=s(25487),c=s(23861),d=s(72713),u=s(381);let h=[{name:"Dashboard",href:"/",icon:l.A},{name:"Sites",href:"/sites",icon:o.A},{name:"Alerts",href:"/alerts",icon:c.A},{name:"Analytics",href:"/analytics",icon:d.A},{name:"Settings",href:"/settings",icon:u.A}];function m(){let e=(0,n.usePathname)();return(0,a.jsxs)("div",{className:"flex h-full flex-col bg-gray-900 text-white w-64",children:[(0,a.jsx)("div",{className:"flex h-16 shrink-0 items-center px-6 border-b border-gray-800",children:(0,a.jsx)("h1",{className:"text-xl font-bold",children:"CDR Site Monitor"})}),(0,a.jsx)("nav",{className:"flex flex-1 flex-col py-4",children:(0,a.jsx)("ul",{className:"flex flex-1 flex-col gap-y-4 px-4",children:h.map(t=>(0,a.jsx)("li",{children:(0,a.jsxs)(r(),{href:t.href,className:"flex items-center gap-x-3 rounded-md px-3 py-2 text-sm font-medium ".concat(e===t.href?"bg-gray-800 text-white":"text-gray-400 hover:bg-gray-800 hover:text-white"),children:[(0,a.jsx)(t.icon,{className:"h-5 w-5","aria-hidden":"true"}),t.name]})},t.name))})}),(0,a.jsx)("div",{className:"border-t border-gray-800 p-4",children:(0,a.jsx)("div",{className:"flex items-center gap-x-3",children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-800"})})})]})}function x(e){let{children:t}=e;return(0,a.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[(0,a.jsx)(m,{}),(0,a.jsx)("main",{className:"flex-1 overflow-auto",children:(0,a.jsx)("div",{className:"p-6",children:t})})]})}},62525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},84616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}},e=>{e.O(0,[213,967,53,441,964,358],()=>e(e.s=22325)),_N_E=e.O()}]);