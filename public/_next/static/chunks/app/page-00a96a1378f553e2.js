(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{14186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},25731:(e,s,t)=>{"use strict";t.d(s,{Ao:()=>i,LB:()=>l,yD:()=>c});let a=t(23464).A.create({baseURL:"http://localhost:3000/api",headers:{"Content-Type":"application/json"}}),l={getAll:async()=>(await a.get("/sites")).data,getById:async e=>(await a.get("/sites/".concat(e))).data,create:async e=>(await a.post("/sites",{site:e})).data,update:async(e,s)=>(await a.put("/sites/".concat(e),{site:s})).data,delete:async e=>{await a.delete("/sites/".concat(e))},getFailedJobs:async e=>(await a.get("/sites/".concat(e,"/failed_jobs"))).data},c={getBySiteId:async(e,s,t)=>{let l=new URLSearchParams;s&&l.append("start_date",s.toISOString()),t&&l.append("end_date",t.toISOString());let c="/sites/".concat(e,"/health_checks").concat(l.toString()?"?".concat(l.toString()):"");return(await a.get(c)).data},getById:async e=>(await a.get("/health_checks/".concat(e))).data,create:async(e,s)=>(await a.post("/sites/".concat(e,"/health_checks"),{health_check:s})).data,getAllWithDateRange:async(e,s)=>{let t=new URLSearchParams;e&&t.append("start_date",e.toISOString()),s&&t.append("end_date",s.toISOString());let l="/health_checks".concat(t.toString()?"?".concat(t.toString()):"");return(await a.get(l)).data}},i={getBySiteId:async e=>(await a.get("/sites/".concat(e,"/alerts"))).data,create:async(e,s)=>(await a.post("/sites/".concat(e,"/alerts"),{alert:s})).data,update:async(e,s)=>(await a.put("/alerts/".concat(e),{alert:s})).data,delete:async e=>{await a.delete("/alerts/".concat(e))}}},30207:(e,s,t)=>{Promise.resolve().then(t.bind(t,33792))},33786:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},33792:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(95155),l=t(32960),c=t(25731),i=t(47080),d=t(1243),r=t(25487),n=t(40646),x=t(14186),h=t(33786),m=t(6874),o=t.n(m);function g(){let{data:e=[],isLoading:s,error:t}=(0,l.I)({queryKey:["sites"],queryFn:c.LB.getAll});if(s)return(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})});if(t)return(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-red-400"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error loading sites"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-red-700",children:"Unable to connect to the monitoring service. Please check if the Rails server is running."})]})]})})});let m=e.filter(e=>"up"===e.status),g=e.filter(e=>"down"===e.status),j=e.filter(e=>"unknown"===e.status);return(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Monitor the health and performance of your Rails applications"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(r.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Sites"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.length})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(n.A,{className:"h-6 w-6 text-green-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Sites Up"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-green-600",children:m.length})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(d.A,{className:"h-6 w-6 text-red-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Sites Down"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-red-600",children:g.length})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(x.A,{className:"h-6 w-6 text-yellow-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Unknown Status"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-yellow-600",children:j.length})]})})]})})})]}),(0,a.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,a.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Recent Site Status"}),(0,a.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Overview of all monitored sites and their current status"})]}),(0,a.jsx)("ul",{className:"divide-y divide-gray-200",children:e.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)("div",{className:"px-4 py-4 sm:px-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"flex-shrink-0",children:["up"===e.status&&(0,a.jsx)(n.A,{className:"h-5 w-5 text-green-400"}),"down"===e.status&&(0,a.jsx)(d.A,{className:"h-5 w-5 text-red-400"}),"unknown"===e.status&&(0,a.jsx)(x.A,{className:"h-5 w-5 text-yellow-400"})]}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsxs)(o(),{href:"/sites/".concat(e.id),className:"group flex items-center",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 group-hover:text-blue-600",children:e.name}),(0,a.jsx)(h.A,{className:"h-3 w-3 ml-1 text-gray-400 group-hover:text-blue-600"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.ip_address,":",e.port]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e.response_time&&(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.response_time.toFixed(0),"ms"]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.last_check_at?new Date(e.last_check_at).toLocaleString():"Never checked"})]})]})})},e.id))}),0===e.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(r.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No sites"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by adding your first site to monitor."})]})]})]})})}},40646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47080:(e,s,t)=>{"use strict";t.d(s,{A:()=>g});var a=t(95155),l=t(6874),c=t.n(l),i=t(35695),d=t(57340),r=t(25487),n=t(23861),x=t(72713),h=t(381);let m=[{name:"Dashboard",href:"/",icon:d.A},{name:"Sites",href:"/sites",icon:r.A},{name:"Alerts",href:"/alerts",icon:n.A},{name:"Analytics",href:"/analytics",icon:x.A},{name:"Settings",href:"/settings",icon:h.A}];function o(){let e=(0,i.usePathname)();return(0,a.jsxs)("div",{className:"flex h-full flex-col bg-gray-900 text-white w-64",children:[(0,a.jsx)("div",{className:"flex h-16 shrink-0 items-center px-6 border-b border-gray-800",children:(0,a.jsx)("h1",{className:"text-xl font-bold",children:"CDR Site Monitor"})}),(0,a.jsx)("nav",{className:"flex flex-1 flex-col py-4",children:(0,a.jsx)("ul",{className:"flex flex-1 flex-col gap-y-4 px-4",children:m.map(s=>(0,a.jsx)("li",{children:(0,a.jsxs)(c(),{href:s.href,className:"flex items-center gap-x-3 rounded-md px-3 py-2 text-sm font-medium ".concat(e===s.href?"bg-gray-800 text-white":"text-gray-400 hover:bg-gray-800 hover:text-white"),children:[(0,a.jsx)(s.icon,{className:"h-5 w-5","aria-hidden":"true"}),s.name]})},s.name))})}),(0,a.jsx)("div",{className:"border-t border-gray-800 p-4",children:(0,a.jsx)("div",{className:"flex items-center gap-x-3",children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-800"})})})]})}function g(e){let{children:s}=e;return(0,a.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[(0,a.jsx)(o,{}),(0,a.jsx)("main",{className:"flex-1 overflow-auto",children:(0,a.jsx)("div",{className:"p-6",children:s})})]})}}},e=>{e.O(0,[213,967,53,441,964,358],()=>e(e.s=30207)),_N_E=e.O()}]);