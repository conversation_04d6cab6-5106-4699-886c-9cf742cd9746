(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[437],{5041:(t,e,s)=>{"use strict";s.d(e,{n:()=>d});var a=s(12115),i=s(34560),r=s(7165),n=s(25910),o=s(52020),l=class extends n.Q{#t;#e=void 0;#s;#a;constructor(t,e){super(),this.#t=t,this.setOptions(e),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#t.defaultMutationOptions(t),(0,o.f8)(this.options,e)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,o.EN)(e.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(t){this.#i(),this.#r(t)}getCurrentResult(){return this.#e}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#i(),this.#r()}mutate(t,e){return this.#a=e,this.#s?.removeObserver(this),this.#s=this.#t.getMutationCache().build(this.#t,this.options),this.#s.addObserver(this),this.#s.execute(t)}#i(){let t=this.#s?.state??(0,i.$)();this.#e={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#r(t){r.jG.batch(()=>{if(this.#a&&this.hasListeners()){let e=this.#e.variables,s=this.#e.context;t?.type==="success"?(this.#a.onSuccess?.(t.data,e,s),this.#a.onSettled?.(t.data,null,e,s)):t?.type==="error"&&(this.#a.onError?.(t.error,e,s),this.#a.onSettled?.(void 0,t.error,e,s))}this.listeners.forEach(t=>{t(this.#e)})})}},c=s(26715);function d(t,e){let s=(0,c.jE)(e),[i]=a.useState(()=>new l(s,t));a.useEffect(()=>{i.setOptions(t)},[i,t]);let n=a.useSyncExternalStore(a.useCallback(t=>i.subscribe(r.jG.batchCalls(t)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),d=a.useCallback((t,e)=>{i.mutate(t,e).catch(o.lQ)},[i]);if(n.error&&(0,o.GU)(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:d,mutateAsync:n.mutate}}},13717:(t,e,s)=>{"use strict";s.d(e,{A:()=>a});let a=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(t,e,s)=>{"use strict";s.d(e,{A:()=>a});let a=(0,s(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},25731:(t,e,s)=>{"use strict";s.d(e,{Ao:()=>n,LB:()=>i,yD:()=>r});let a=s(23464).A.create({baseURL:"http://localhost:3000/api",headers:{"Content-Type":"application/json"}}),i={getAll:async()=>(await a.get("/sites")).data,getById:async t=>(await a.get("/sites/".concat(t))).data,create:async t=>(await a.post("/sites",{site:t})).data,update:async(t,e)=>(await a.put("/sites/".concat(t),{site:e})).data,delete:async t=>{await a.delete("/sites/".concat(t))},getFailedJobs:async t=>(await a.get("/sites/".concat(t,"/failed_jobs"))).data},r={getBySiteId:async(t,e,s)=>{let i=new URLSearchParams;e&&i.append("start_date",e.toISOString()),s&&i.append("end_date",s.toISOString());let r="/sites/".concat(t,"/health_checks").concat(i.toString()?"?".concat(i.toString()):"");return(await a.get(r)).data},getById:async t=>(await a.get("/health_checks/".concat(t))).data,create:async(t,e)=>(await a.post("/sites/".concat(t,"/health_checks"),{health_check:e})).data,getAllWithDateRange:async(t,e)=>{let s=new URLSearchParams;t&&s.append("start_date",t.toISOString()),e&&s.append("end_date",e.toISOString());let i="/health_checks".concat(s.toString()?"?".concat(s.toString()):"");return(await a.get(i)).data}},n={getBySiteId:async t=>(await a.get("/sites/".concat(t,"/alerts"))).data,create:async(t,e)=>(await a.post("/sites/".concat(t,"/alerts"),{alert:e})).data,update:async(t,e)=>(await a.put("/alerts/".concat(t),{alert:e})).data,delete:async t=>{await a.delete("/alerts/".concat(t))}}},33786:(t,e,s)=>{"use strict";s.d(e,{A:()=>a});let a=(0,s(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34560:(t,e,s)=>{"use strict";s.d(e,{$:()=>o,s:()=>n});var a=s(7165),i=s(57948),r=s(6784),n=class extends i.k{#n;#o;#l;constructor(t){super(),this.mutationId=t.mutationId,this.#o=t.mutationCache,this.#n=[],this.state=t.state||o(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#n.includes(t)||(this.#n.push(t),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#n=this.#n.filter(e=>e!==t),this.scheduleGc(),this.#o.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#n.length||("pending"===this.state.status?this.scheduleGc():this.#o.remove(this))}continue(){return this.#l?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#c({type:"continue"})};this.#l=(0,r.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#c({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#c({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#o.canRun(this)});let s="pending"===this.state.status,a=!this.#l.canStart();try{if(s)e();else{this.#c({type:"pending",variables:t,isPaused:a}),await this.#o.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#c({type:"pending",context:e,variables:t,isPaused:a})}let i=await this.#l.start();return await this.#o.config.onSuccess?.(i,t,this.state.context,this),await this.options.onSuccess?.(i,t,this.state.context),await this.#o.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,t,this.state.context),this.#c({type:"success",data:i}),i}catch(e){try{throw await this.#o.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#o.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#c({type:"error",error:e})}}finally{this.#o.runNext(this)}}#c(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),a.jG.batch(()=>{this.#n.forEach(e=>{e.onMutationUpdate(t)}),this.#o.notify({mutation:this,type:"updated",action:t})})}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},39603:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>g});var a=s(95155),i=s(12115),r=s(26715),n=s(32960),o=s(5041),l=s(25731),c=s(47080),d=s(1243),u=s(84616),h=s(40646),m=s(14186),x=s(33786),p=s(13717),y=s(62525),b=s(6874),f=s.n(b);function g(){let[t,e]=(0,i.useState)(!1),[s,b]=(0,i.useState)(null),g=(0,r.jE)(),{data:v=[],isLoading:j,error:w}=(0,n.I)({queryKey:["sites"],queryFn:l.LB.getAll}),N=(0,o.n)({mutationFn:l.LB.create,onSuccess:()=>{g.invalidateQueries({queryKey:["sites"]}),e(!1)}}),k=(0,o.n)({mutationFn:t=>{let{id:e,...s}=t;return l.LB.update(e,s)},onSuccess:()=>{g.invalidateQueries({queryKey:["sites"]}),b(null)}}),S=(0,o.n)({mutationFn:l.LB.delete,onSuccess:()=>{g.invalidateQueries({queryKey:["sites"]})}});return j?(0,a.jsx)(c.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})}):w?(0,a.jsx)(c.A,{children:(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-red-400"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error loading sites"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-red-700",children:"Unable to connect to the monitoring service."})]})]})})}):(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Sites"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Manage the sites you want to monitor"})]}),(0,a.jsxs)("button",{onClick:()=>e(!0),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Add Site"]})]}),(t||s)&&(0,a.jsx)("div",{className:"bg-white shadow sm:rounded-lg",children:(0,a.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:s?"Edit Site":"Add New Site"}),(0,a.jsxs)("form",{onSubmit:t=>{t.preventDefault();let e=new FormData(t.currentTarget),a={name:e.get("name"),ip_address:e.get("ip_address"),port:parseInt(e.get("port")),status:"unknown",last_check_at:null,response_time:null};s?k.mutate({id:s.id,...a}):N.mutate(a)},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Site Name"}),(0,a.jsx)("input",{type:"text",name:"name",id:"name",required:!0,defaultValue:(null==s?void 0:s.name)||"",className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black p-2",placeholder:"My Application"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"ip_address",className:"block text-sm font-medium text-gray-700",children:"IP Address"}),(0,a.jsx)("input",{type:"text",name:"ip_address",id:"ip_address",required:!0,defaultValue:(null==s?void 0:s.ip_address)||"",className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black p-2",placeholder:"*************"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"port",className:"block text-sm font-medium text-gray-700",children:"Port"}),(0,a.jsx)("input",{type:"number",name:"port",id:"port",required:!0,min:"1",max:"65535",defaultValue:(null==s?void 0:s.port)||"",className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black p-2",placeholder:"3000"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{e(!1),b(null)},className:"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:N.isPending||k.isPending,className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:N.isPending||k.isPending?"Saving...":"Save"})]})]})]})}),(0,a.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,a.jsx)("div",{className:"px-4 py-5 sm:px-6",children:(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Monitored Sites"})}),(0,a.jsx)("ul",{className:"divide-y divide-gray-200",children:v.map(t=>(0,a.jsx)("li",{children:(0,a.jsx)("div",{className:"px-4 py-4 sm:px-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"flex-shrink-0",children:["up"===t.status&&(0,a.jsx)(h.A,{className:"h-5 w-5 text-green-400"}),"down"===t.status&&(0,a.jsx)(d.A,{className:"h-5 w-5 text-red-400"}),"unknown"===t.status&&(0,a.jsx)(m.A,{className:"h-5 w-5 text-yellow-400"})]}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsxs)(f(),{href:"/sites/".concat(t.id),className:"group flex items-center",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 group-hover:text-blue-600",children:t.name}),(0,a.jsx)(x.A,{className:"h-3 w-3 ml-1 text-gray-400 group-hover:text-blue-600"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[t.ip_address,":",t.port]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>b(t),className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{confirm("Are you sure you want to delete this site?")&&S.mutate(t.id)},className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]})})},t.id))}),0===v.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No sites"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by adding your first site to monitor."})]})]})]})})}},40548:(t,e,s)=>{Promise.resolve().then(s.bind(s,39603))},40646:(t,e,s)=>{"use strict";s.d(e,{A:()=>a});let a=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47080:(t,e,s)=>{"use strict";s.d(e,{A:()=>x});var a=s(95155),i=s(6874),r=s.n(i),n=s(35695),o=s(57340),l=s(25487),c=s(23861),d=s(72713),u=s(381);let h=[{name:"Dashboard",href:"/",icon:o.A},{name:"Sites",href:"/sites",icon:l.A},{name:"Alerts",href:"/alerts",icon:c.A},{name:"Analytics",href:"/analytics",icon:d.A},{name:"Settings",href:"/settings",icon:u.A}];function m(){let t=(0,n.usePathname)();return(0,a.jsxs)("div",{className:"flex h-full flex-col bg-gray-900 text-white w-64",children:[(0,a.jsx)("div",{className:"flex h-16 shrink-0 items-center px-6 border-b border-gray-800",children:(0,a.jsx)("h1",{className:"text-xl font-bold",children:"CDR Site Monitor"})}),(0,a.jsx)("nav",{className:"flex flex-1 flex-col py-4",children:(0,a.jsx)("ul",{className:"flex flex-1 flex-col gap-y-4 px-4",children:h.map(e=>(0,a.jsx)("li",{children:(0,a.jsxs)(r(),{href:e.href,className:"flex items-center gap-x-3 rounded-md px-3 py-2 text-sm font-medium ".concat(t===e.href?"bg-gray-800 text-white":"text-gray-400 hover:bg-gray-800 hover:text-white"),children:[(0,a.jsx)(e.icon,{className:"h-5 w-5","aria-hidden":"true"}),e.name]})},e.name))})}),(0,a.jsx)("div",{className:"border-t border-gray-800 p-4",children:(0,a.jsx)("div",{className:"flex items-center gap-x-3",children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-800"})})})]})}function x(t){let{children:e}=t;return(0,a.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[(0,a.jsx)(m,{}),(0,a.jsx)("main",{className:"flex-1 overflow-auto",children:(0,a.jsx)("div",{className:"p-6",children:e})})]})}},62525:(t,e,s)=>{"use strict";s.d(e,{A:()=>a});let a=(0,s(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},84616:(t,e,s)=>{"use strict";s.d(e,{A:()=>a});let a=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}},t=>{t.O(0,[213,967,53,441,964,358],()=>t(t.s=40548)),_N_E=t.O()}]);