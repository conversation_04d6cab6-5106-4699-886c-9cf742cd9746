(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[113],{25731:(e,s,t)=>{"use strict";t.d(s,{Ao:()=>d,LB:()=>l,yD:()=>r});let a=t(23464).A.create({baseURL:"http://localhost:3000/api",headers:{"Content-Type":"application/json"}}),l={getAll:async()=>(await a.get("/sites")).data,getById:async e=>(await a.get("/sites/".concat(e))).data,create:async e=>(await a.post("/sites",{site:e})).data,update:async(e,s)=>(await a.put("/sites/".concat(e),{site:s})).data,delete:async e=>{await a.delete("/sites/".concat(e))},getFailedJobs:async e=>(await a.get("/sites/".concat(e,"/failed_jobs"))).data},r={getBySiteId:async(e,s,t)=>{let l=new URLSearchParams;s&&l.append("start_date",s.toISOString()),t&&l.append("end_date",t.toISOString());let r="/sites/".concat(e,"/health_checks").concat(l.toString()?"?".concat(l.toString()):"");return(await a.get(r)).data},getById:async e=>(await a.get("/health_checks/".concat(e))).data,create:async(e,s)=>(await a.post("/sites/".concat(e,"/health_checks"),{health_check:s})).data,getAllWithDateRange:async(e,s)=>{let t=new URLSearchParams;e&&t.append("start_date",e.toISOString()),s&&t.append("end_date",s.toISOString());let l="/health_checks".concat(t.toString()?"?".concat(t.toString()):"");return(await a.get(l)).data}},d={getBySiteId:async e=>(await a.get("/sites/".concat(e,"/alerts"))).data,create:async(e,s)=>(await a.post("/sites/".concat(e,"/alerts"),{alert:s})).data,update:async(e,s)=>(await a.put("/alerts/".concat(e),{alert:s})).data,delete:async e=>{await a.delete("/alerts/".concat(e))}}},36669:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var a=t(95155),l=t(12115),r=t(54239),d=t(69074),i=t(54416),n=t(6711),c=t(19828),o=t(9107);function x(e){let{startDate:s,endDate:t,onDateRangeChange:x,className:m=""}=e,[h,g]=(0,l.useState)(!1),u=()=>{x(null,null)},j=e=>{let s=(0,c.D)(new Date);x((0,n.o)((0,o.e)(s,e-1)),s)};return(0,a.jsxs)("div",{className:"relative ".concat(m),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>g(!h),className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),s||t?s&&!t?"From ".concat(s.toLocaleDateString()):!s&&t?"Until ".concat(t.toLocaleDateString()):"".concat(null==s?void 0:s.toLocaleDateString()," - ").concat(null==t?void 0:t.toLocaleDateString()):"Select date range"]}),(s||t)&&(0,a.jsx)("button",{onClick:u,className:"inline-flex items-center p-1 border border-gray-300 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-50",title:"Clear date filter",children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})]}),h&&(0,a.jsx)("div",{className:"absolute top-full right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4 min-w-max",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)("button",{onClick:()=>j(1),className:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md text-black",children:"Today"}),(0,a.jsx)("button",{onClick:()=>j(7),className:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md text-black",children:"Last 7 days"}),(0,a.jsx)("button",{onClick:()=>j(30),className:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md text-black",children:"Last 30 days"}),(0,a.jsx)("button",{onClick:()=>j(90),className:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md  text-black",children:"Last 90 days"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1 text-black",children:"Start Date"}),(0,a.jsx)(r.Ay,{selected:s,onChange:e=>{x(e?(0,n.o)(e):null,t)},selectsStart:!0,startDate:s,endDate:t,maxDate:new Date,className:"text-black w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholderText:"Select start date",dateFormat:"MMM dd, yyyy"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1  text-black",children:"End Date"}),(0,a.jsx)(r.Ay,{selected:t,onChange:e=>{x(s,e?(0,c.D)(e):null)},selectsEnd:!0,startDate:s,endDate:t,minDate:s||void 0,maxDate:new Date,className:"text-black w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholderText:"Select end date",dateFormat:"MMM dd, yyyy"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-2 border-t",children:[(0,a.jsx)("button",{onClick:()=>g(!1),className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-800  text-black",children:"Close"}),(0,a.jsx)("button",{onClick:()=>{u(),g(!1)},className:"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md text-black",children:"Clear"})]})]})})]})}t(35279)},47080:(e,s,t)=>{"use strict";t.d(s,{A:()=>g});var a=t(95155),l=t(6874),r=t.n(l),d=t(35695),i=t(57340),n=t(25487),c=t(23861),o=t(72713),x=t(381);let m=[{name:"Dashboard",href:"/",icon:i.A},{name:"Sites",href:"/sites",icon:n.A},{name:"Alerts",href:"/alerts",icon:c.A},{name:"Analytics",href:"/analytics",icon:o.A},{name:"Settings",href:"/settings",icon:x.A}];function h(){let e=(0,d.usePathname)();return(0,a.jsxs)("div",{className:"flex h-full flex-col bg-gray-900 text-white w-64",children:[(0,a.jsx)("div",{className:"flex h-16 shrink-0 items-center px-6 border-b border-gray-800",children:(0,a.jsx)("h1",{className:"text-xl font-bold",children:"CDR Site Monitor"})}),(0,a.jsx)("nav",{className:"flex flex-1 flex-col py-4",children:(0,a.jsx)("ul",{className:"flex flex-1 flex-col gap-y-4 px-4",children:m.map(s=>(0,a.jsx)("li",{children:(0,a.jsxs)(r(),{href:s.href,className:"flex items-center gap-x-3 rounded-md px-3 py-2 text-sm font-medium ".concat(e===s.href?"bg-gray-800 text-white":"text-gray-400 hover:bg-gray-800 hover:text-white"),children:[(0,a.jsx)(s.icon,{className:"h-5 w-5","aria-hidden":"true"}),s.name]})},s.name))})}),(0,a.jsx)("div",{className:"border-t border-gray-800 p-4",children:(0,a.jsx)("div",{className:"flex items-center gap-x-3",children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-800"})})})]})}function g(e){let{children:s}=e;return(0,a.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[(0,a.jsx)(h,{}),(0,a.jsx)("main",{className:"flex-1 overflow-auto",children:(0,a.jsx)("div",{className:"p-6",children:s})})]})}},63713:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>K});var a=t(95155),l=t(12115),r=t(32960),d=t(35695),i=t(25731),n=t(47080),c=t(36669),o=t(1243),x=t(53904),m=t(54416),h=t(29621),g=t(14186),u=t(26681);function j(e){var s;let{siteId:t,siteName:d,isOpen:n,onClose:c}=e,[j,y]=(0,l.useState)(null),{data:p,isLoading:f,error:b,refetch:N}=(0,r.I)({queryKey:["failedJobs",t],queryFn:()=>i.LB.getFailedJobs(t),enabled:n});if(!n)return null;let v=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200;return e.length<=s?e:e.substring(0,s)+"..."};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(o.A,{className:"h-6 w-6 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Failed Jobs"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:d})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{N()},disabled:f,className:"p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 text-black",title:"Refresh failed jobs",children:(0,a.jsx)(x.A,{className:"h-5 w-5 ".concat(f?"animate-spin":"")})}),(0,a.jsx)("button",{onClick:c,className:"p-2 rounded-md hover:bg-gray-100  text-black",children:(0,a.jsx)(m.A,{className:"h-5 w-5"})})]})]}),(0,a.jsx)("div",{className:"flex-1 overflow-auto p-6",children:f?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):b?(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-red-400"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error loading failed jobs"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-red-700",children:(null==p?void 0:p.error)||"Unable to fetch failed jobs from the monitored site."})]})]})}):(null==p||null==(s=p.failed_jobs)?void 0:s.length)?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Showing ",p.failed_jobs.length," failed job",1!==p.failed_jobs.length?"s":""]})}),p.failed_jobs.map(e=>(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:e.job_class}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full",children:e.queue_name}),e.priority>0&&(0,a.jsxs)("span",{className:"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full",children:["Priority: ",e.priority]})]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(g.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:["Failed: ",(0,u.GP)(new Date(e.failed_at),"MMM dd, yyyy HH:mm:ss")]})]}),e.retry_count>0&&(0,a.jsxs)("span",{children:["Retries: ",e.retry_count]}),e.active_job_id&&(0,a.jsxs)("span",{children:["Job ID: ",e.active_job_id]})]}),(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Error:"}),(0,a.jsx)("p",{className:"text-sm text-gray-700 mt-1 font-mono bg-red-50 p-2 rounded border",children:j===e.id?e.error_message:v(e.error_message)})]})]}),(0,a.jsx)("button",{onClick:()=>{var s;y(j===(s=e.id)?null:s)},className:"ml-4 px-3 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded-md text-black",children:j===e.id?"Less":"More"})]}),j===e.id&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200 space-y-3",children:[e.arguments&&Object.keys(e.arguments).length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Arguments:"}),(0,a.jsx)("pre",{className:"text-xs text-gray-600 mt-1 bg-gray-100 p-2 rounded border overflow-x-auto",children:(e=>{if(!e||"object"!=typeof e)return"None";try{return JSON.stringify(e,null,2)}catch(s){return String(e)}})(e.arguments)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs",children:[e.scheduled_at&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-700",children:"Scheduled At:"}),(0,a.jsx)("p",{className:"text-gray-600",children:(0,u.GP)(new Date(e.scheduled_at),"MMM dd, yyyy HH:mm:ss")})]}),e.finished_at&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-700",children:"Finished At:"}),(0,a.jsx)("p",{className:"text-gray-600",children:(0,u.GP)(new Date(e.finished_at),"MMM dd, yyyy HH:mm:ss")})]})]})]})]})},e.id))]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(o.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No failed jobs"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Great! There are currently no failed jobs for this site."})]})}),(0,a.jsx)("div",{className:"border-t p-4 bg-gray-50",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Data fetched from ",d," • Last updated: ",new Date().toLocaleTimeString()]}),(0,a.jsx)("button",{onClick:c,className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm",children:"Close"})]})})]})})}var y=t(83540),p=t(93504),f=t(94754),b=t(96025),N=t(52071),v=t(27086),w=t(13279),k=t(90170),_=t(18357),D=t(54811),A=t(99445),S=t(61667),C=t(6711),F=t(19828),L=t(5535),M=t(2845),B=t(35169),I=t(40646),T=t(79397),P=t(71539),R=t(25487),E=t(17580),H=t(6874),J=t.n(H);function K(){let e=parseInt((0,d.useParams)().id),[s,t]=(0,l.useState)((0,C.o)(new Date)),[m,h]=(0,l.useState)((0,F.D)(new Date)),[H,K]=(0,l.useState)(!1),{data:q,isLoading:G,refetch:O}=(0,r.I)({queryKey:["sites",e],queryFn:()=>i.LB.getById(e)}),{data:U=[],isLoading:W,refetch:Q}=(0,r.I)({queryKey:["healthChecks",e,s,m],queryFn:()=>i.yD.getBySiteId(e,s,m)}),V=async()=>{await Promise.all([O(),Q()])};if(G||W)return(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})});if(!q)return(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Site not found"})})});let z=U.slice(-20).map(e=>({time:(0,u.GP)(new Date(e.created_at),"HH:mm"),responseTime:e.response_time||0,status:e.status,jobsDone:e.jobs_done||0,jobsPending:e.jobs_pending||0,jobsFailed:e.jobs_failed||0,jobsQueued:e.jobs_queued||0,visitorCount:e.visitor_count||0})),X=U.reduce((e,s)=>e+(s.response_time||0),0)/U.length,Y=U.filter(e=>"up"===e.status).length/U.length*100,Z=U[0],$=U.find(e=>"up"===e.status),ee=Z?(Z.jobs_done||0)+(Z.jobs_pending||0)+(Z.jobs_failed||0)+(Z.jobs_queued||0):0,es=Z?[{name:"Done",value:Z.jobs_done||0,color:"#10B981"},{name:"Pending",value:Z.jobs_pending||0,color:"#F59E0B"},{name:"Failed",value:Z.jobs_failed||0,color:"#EF4444"},{name:"Queued",value:Z.jobs_queued||0,color:"#8B5CF6"}]:[];return(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 text-black pointer-cursor",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(J(),{href:"/sites",className:"p-2 rounded-md hover:bg-gray-100",children:(0,a.jsx)(B.A,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:V,disabled:G||W,className:"p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed text-black pointer-cursor",title:"Refresh data",children:(0,a.jsx)(x.A,{className:"h-5 w-5 ".concat(G||W?"animate-spin":"")})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:q.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[q.ip_address,":",q.port]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 ",children:[(0,a.jsx)(c.A,{startDate:s,endDate:m,onDateRangeChange:(e,s)=>{t(e),h(s)}}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:["up"===q.status&&(0,a.jsx)(I.A,{className:"h-6 w-6 text-green-500"}),"down"===q.status&&(0,a.jsx)(o.A,{className:"h-6 w-6 text-red-500"}),"unknown"===q.status&&(0,a.jsx)(g.A,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("up"===q.status?"bg-green-100 text-green-800":"down"===q.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:q.status.toUpperCase()}),"down"===q.status&&$&&(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:["Last Seen ",((e,s)=>{let t=(0,L.o)(new Date,e),a=(0,M.M)(new Date,e);return t<1?"just now":t<60?"".concat(t,"m ago"):a<24?"".concat(a,"h ago"):"".concat(Math.floor(a/24),"d ago")})(new Date($.created_at),0)]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(T.A,{className:"h-6 w-6 text-blue-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Uptime (Last 10)"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[Y.toFixed(1),"%"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(P.A,{className:"h-6 w-6 text-yellow-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Avg Response Time"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[X.toFixed(0),"ms"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(R.A,{className:"h-6 w-6 text-purple-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Jobs"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:ee})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(E.A,{className:"h-6 w-6 text-green-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Visits"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null==Z?void 0:Z.visitor_count)||0})]})})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Response Time Trend"}),(0,a.jsx)(y.u,{width:"100%",height:300,children:(0,a.jsxs)(p.b,{data:z,children:[(0,a.jsx)(f.d,{strokeDasharray:"3 3"}),(0,a.jsx)(b.W,{dataKey:"time"}),(0,a.jsx)(N.h,{}),(0,a.jsx)(v.m,{formatter:(e,s)=>["".concat(e,"ms"),"Response Time"],labelFormatter:e=>"Time: ".concat(e)}),(0,a.jsx)(w.N,{type:"monotone",dataKey:"responseTime",stroke:"#3B82F6",strokeWidth:2,dot:{fill:"#3B82F6",strokeWidth:2,r:4}})]})})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Current Jobs Distribution"}),Z&&Z.jobs_failed>0&&(0,a.jsxs)("button",{onClick:()=>K(!0),className:"inline-flex items-center px-3 py-1 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-1"}),"View Failed Jobs (",Z.jobs_failed,")"]})]}),(0,a.jsx)(y.u,{width:"100%",height:300,children:(0,a.jsxs)(k.r,{children:[(0,a.jsx)(_.F,{data:es,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:s,value:t}=e;return"".concat(s,": ").concat(t)},outerRadius:80,fill:"#8884d8",dataKey:"value",children:es.map((e,s)=>(0,a.jsx)(D.f,{fill:e.color},"cell-".concat(s)))}),(0,a.jsx)(v.m,{})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Jobs Over Time"}),(0,a.jsx)(y.u,{width:"100%",height:300,children:(0,a.jsxs)(A.Q,{data:z,children:[(0,a.jsx)(f.d,{strokeDasharray:"3 3"}),(0,a.jsx)(b.W,{dataKey:"time"}),(0,a.jsx)(N.h,{}),(0,a.jsx)(v.m,{}),(0,a.jsx)(S.Gk,{type:"monotone",dataKey:"jobsDone",stackId:"1",stroke:"#10B981",fill:"#10B981"}),(0,a.jsx)(S.Gk,{type:"monotone",dataKey:"jobsPending",stackId:"1",stroke:"#F59E0B",fill:"#F59E0B"}),(0,a.jsx)(S.Gk,{type:"monotone",dataKey:"jobsFailed",stackId:"1",stroke:"#EF4444",fill:"#EF4444"}),(0,a.jsx)(S.Gk,{type:"monotone",dataKey:"jobsQueued",stackId:"1",stroke:"#8B5CF6",fill:"#8B5CF6"})]})})]}),(0,a.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,a.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Recent Health Checks"}),(0,a.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Latest health check results for this site"})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Time"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Response Time"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Jobs"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Visits"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:U.slice(0,10).map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,u.GP)(new Date(e.created_at),"MMM dd, HH:mm:ss")}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("up"===e.status?"bg-green-100 text-green-800":"down"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.response_time?"".concat(e.response_time.toFixed(0),"ms"):"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsxs)("div",{children:["Done: ",e.jobs_done||0]}),(0,a.jsxs)("div",{children:["Failed: ",e.jobs_failed||0]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.visitor_count||0})]},e.id))})]})})]}),(0,a.jsx)(j,{siteId:e,siteName:q.name,isOpen:H,onClose:()=>K(!1)})]})})}},75398:(e,s,t)=>{Promise.resolve().then(t.bind(t,63713))}},e=>{e.O(0,[302,579,20,213,967,53,126,75,441,964,358],()=>e(e.s=75398)),_N_E=e.O()}]);