(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[745],{18495:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>M});var a=s(95155),l=s(12115),r=s(32960),d=s(25731),n=s(47080),i=s(36669),c=s(83540),x=s(93504),o=s(94754),h=s(96025),m=s(52071),g=s(27086),u=s(13279),y=s(90170),p=s(18357),j=s(54811),f=s(3401),w=s(56690),b=s(64683),N=s(6711),v=s(9107),k=s(19828),D=s(26681),S=s(79397),A=s(33109),C=s(1243),_=s(68500);function M(){let[e,t]=(0,l.useState)((0,N.o)((0,v.e)(new Date,6))),[s,M]=(0,l.useState)((0,k.D)(new Date)),{data:F=[],isLoading:L}=(0,r.I)({queryKey:["sites"],queryFn:d.LB.getAll}),R=(0,r.I)({queryKey:["allHealthChecks",e,s],queryFn:()=>d.yD.getAllWithDateRange(e,s)});if(L||R.isLoading)return(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})});let T=R.data||[],B=(()=>{if(!e||!s)return Array.from({length:7},(e,t)=>{let s=(0,v.e)(new Date,6-t);return(0,N.o)(s)});let t=[],a=new Date(e),l=new Date(s);for(;a<=l;)t.push(new Date(a)),a.setDate(a.getDate()+1);return t})().map(e=>{let t=new Date(e.getTime()+864e5),s=T.filter(s=>{let a=new Date(s.created_at);return a>=e&&a<t}),a=s.filter(e=>"up"===e.status),l=s.filter(e=>"down"===e.status);return{date:(0,D.GP)(e,"MMM dd"),totalChecks:s.length,upChecks:a.length,downChecks:l.length,avgResponseTime:a.length>0?a.reduce((e,t)=>e+(t.response_time||0),0)/a.length:0,uptime:s.length>0?a.length/s.length*100:0}}),I=[{name:"Up",value:F.filter(e=>"up"===e.status).length,color:"#10B981"},{name:"Down",value:F.filter(e=>"down"===e.status).length,color:"#EF4444"},{name:"Unknown",value:F.filter(e=>"unknown"===e.status).length,color:"#F59E0B"}].filter(e=>e.value>0),E=T.length,K=T.filter(e=>"up"===e.status).length,P=E>0?K/E*100:0,U=K>0?T.filter(e=>"up"===e.status&&e.response_time).reduce((e,t)=>e+(t.response_time||0),0)/K:0;return(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Analytics"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Comprehensive analytics and insights for your monitored sites"})]}),(0,a.jsx)(i.A,{startDate:e,endDate:s,onDateRangeChange:(e,s)=>{t(e),M(s)}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(S.A,{className:"h-6 w-6 text-blue-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Overall Uptime"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[P.toFixed(1),"%"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(A.A,{className:"h-6 w-6 text-green-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Avg Response Time"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[U.toFixed(0),"ms"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(C.A,{className:"h-6 w-6 text-yellow-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Checks"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:E})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(_.A,{className:"h-6 w-6 text-red-400"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Failed Checks"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:E-K})]})})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:["Uptime Trend ",e&&s&&"(".concat((0,D.GP)(e,"MMM dd")," - ").concat((0,D.GP)(s,"MMM dd"),")")]}),(0,a.jsx)(c.u,{width:"100%",height:300,children:(0,a.jsxs)(x.b,{data:B,children:[(0,a.jsx)(o.d,{strokeDasharray:"3 3"}),(0,a.jsx)(h.W,{dataKey:"date"}),(0,a.jsx)(m.h,{domain:[0,100]}),(0,a.jsx)(g.m,{formatter:(e,t)=>["".concat(Number(e).toFixed(1),"%"),"Uptime"]}),(0,a.jsx)(u.N,{type:"monotone",dataKey:"uptime",stroke:"#10B981",strokeWidth:3,dot:{fill:"#10B981",strokeWidth:2,r:5}})]})})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Site Status Distribution"}),(0,a.jsx)(c.u,{width:"100%",height:300,children:(0,a.jsxs)(y.r,{children:[(0,a.jsx)(p.F,{data:I,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:t,value:s}=e;return"".concat(t,": ").concat(s)},outerRadius:100,fill:"#8884d8",dataKey:"value",children:I.map((e,t)=>(0,a.jsx)(j.f,{fill:e.color},"cell-".concat(t)))}),(0,a.jsx)(g.m,{})]})})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Average Response Time Trend"}),(0,a.jsx)(c.u,{width:"100%",height:300,children:(0,a.jsxs)(f.E,{data:B,children:[(0,a.jsx)(o.d,{strokeDasharray:"3 3"}),(0,a.jsx)(h.W,{dataKey:"date"}),(0,a.jsx)(m.h,{}),(0,a.jsx)(g.m,{formatter:(e,t)=>["".concat(Number(e).toFixed(0),"ms"),"Avg Response Time"]}),(0,a.jsx)(w.y,{dataKey:"avgResponseTime",fill:"#3B82F6"})]})})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Daily Health Check Volume"}),(0,a.jsx)(c.u,{width:"100%",height:300,children:(0,a.jsxs)(f.E,{data:B,children:[(0,a.jsx)(o.d,{strokeDasharray:"3 3"}),(0,a.jsx)(h.W,{dataKey:"date"}),(0,a.jsx)(m.h,{}),(0,a.jsx)(g.m,{}),(0,a.jsx)(w.y,{dataKey:"upChecks",stackId:"a",fill:"#10B981",name:"Successful"}),(0,a.jsx)(w.y,{dataKey:"downChecks",stackId:"a",fill:"#EF4444",name:"Failed"}),(0,a.jsx)(b.s,{})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,a.jsx)("div",{className:"px-4 py-5 sm:px-6",children:(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Site Performance Summary"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Site"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Health Checks"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Avg Response Time"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Check"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:F.map(e=>{let t=T.filter(t=>t.site_id===e.id),s=t.filter(e=>"up"===e.status),l=s.length>0?s.reduce((e,t)=>e+(t.response_time||0),0)/s.length:0;return(0,a.jsxs)("tr",{children:[(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.ip_address,":",e.port]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("up"===e.status?"bg-green-100 text-green-800":"down"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:t.length}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[l.toFixed(0),"ms"]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.last_check_at?(0,D.GP)(new Date(e.last_check_at),"MMM dd, HH:mm"):"Never"})]},e.id)})})]})})]})]})})}},25731:(e,t,s)=>{"use strict";s.d(t,{Ao:()=>d,LB:()=>l,yD:()=>r});let a=s(23464).A.create({baseURL:"http://localhost:3000/api",headers:{"Content-Type":"application/json"}}),l={getAll:async()=>(await a.get("/sites")).data,getById:async e=>(await a.get("/sites/".concat(e))).data,create:async e=>(await a.post("/sites",{site:e})).data,update:async(e,t)=>(await a.put("/sites/".concat(e),{site:t})).data,delete:async e=>{await a.delete("/sites/".concat(e))},getFailedJobs:async e=>(await a.get("/sites/".concat(e,"/failed_jobs"))).data},r={getBySiteId:async(e,t,s)=>{let l=new URLSearchParams;t&&l.append("start_date",t.toISOString()),s&&l.append("end_date",s.toISOString());let r="/sites/".concat(e,"/health_checks").concat(l.toString()?"?".concat(l.toString()):"");return(await a.get(r)).data},getById:async e=>(await a.get("/health_checks/".concat(e))).data,create:async(e,t)=>(await a.post("/sites/".concat(e,"/health_checks"),{health_check:t})).data,getAllWithDateRange:async(e,t)=>{let s=new URLSearchParams;e&&s.append("start_date",e.toISOString()),t&&s.append("end_date",t.toISOString());let l="/health_checks".concat(s.toString()?"?".concat(s.toString()):"");return(await a.get(l)).data}},d={getBySiteId:async e=>(await a.get("/sites/".concat(e,"/alerts"))).data,create:async(e,t)=>(await a.post("/sites/".concat(e,"/alerts"),{alert:t})).data,update:async(e,t)=>(await a.put("/alerts/".concat(e),{alert:t})).data,delete:async e=>{await a.delete("/alerts/".concat(e))}}},36669:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(95155),l=s(12115),r=s(54239),d=s(69074),n=s(54416),i=s(6711),c=s(19828),x=s(9107);function o(e){let{startDate:t,endDate:s,onDateRangeChange:o,className:h=""}=e,[m,g]=(0,l.useState)(!1),u=()=>{o(null,null)},y=e=>{let t=(0,c.D)(new Date);o((0,i.o)((0,x.e)(t,e-1)),t)};return(0,a.jsxs)("div",{className:"relative ".concat(h),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>g(!m),className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),t||s?t&&!s?"From ".concat(t.toLocaleDateString()):!t&&s?"Until ".concat(s.toLocaleDateString()):"".concat(null==t?void 0:t.toLocaleDateString()," - ").concat(null==s?void 0:s.toLocaleDateString()):"Select date range"]}),(t||s)&&(0,a.jsx)("button",{onClick:u,className:"inline-flex items-center p-1 border border-gray-300 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-50",title:"Clear date filter",children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})]}),m&&(0,a.jsx)("div",{className:"absolute top-full right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4 min-w-max",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)("button",{onClick:()=>y(1),className:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md text-black",children:"Today"}),(0,a.jsx)("button",{onClick:()=>y(7),className:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md text-black",children:"Last 7 days"}),(0,a.jsx)("button",{onClick:()=>y(30),className:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md text-black",children:"Last 30 days"}),(0,a.jsx)("button",{onClick:()=>y(90),className:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md  text-black",children:"Last 90 days"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1 text-black",children:"Start Date"}),(0,a.jsx)(r.Ay,{selected:t,onChange:e=>{o(e?(0,i.o)(e):null,s)},selectsStart:!0,startDate:t,endDate:s,maxDate:new Date,className:"text-black w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholderText:"Select start date",dateFormat:"MMM dd, yyyy"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1  text-black",children:"End Date"}),(0,a.jsx)(r.Ay,{selected:s,onChange:e=>{o(t,e?(0,c.D)(e):null)},selectsEnd:!0,startDate:t,endDate:s,minDate:t||void 0,maxDate:new Date,className:"text-black w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholderText:"Select end date",dateFormat:"MMM dd, yyyy"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-2 border-t",children:[(0,a.jsx)("button",{onClick:()=>g(!1),className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-800  text-black",children:"Close"}),(0,a.jsx)("button",{onClick:()=>{u(),g(!1)},className:"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md text-black",children:"Clear"})]})]})})]})}s(35279)},47080:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var a=s(95155),l=s(6874),r=s.n(l),d=s(35695),n=s(57340),i=s(25487),c=s(23861),x=s(72713),o=s(381);let h=[{name:"Dashboard",href:"/",icon:n.A},{name:"Sites",href:"/sites",icon:i.A},{name:"Alerts",href:"/alerts",icon:c.A},{name:"Analytics",href:"/analytics",icon:x.A},{name:"Settings",href:"/settings",icon:o.A}];function m(){let e=(0,d.usePathname)();return(0,a.jsxs)("div",{className:"flex h-full flex-col bg-gray-900 text-white w-64",children:[(0,a.jsx)("div",{className:"flex h-16 shrink-0 items-center px-6 border-b border-gray-800",children:(0,a.jsx)("h1",{className:"text-xl font-bold",children:"CDR Site Monitor"})}),(0,a.jsx)("nav",{className:"flex flex-1 flex-col py-4",children:(0,a.jsx)("ul",{className:"flex flex-1 flex-col gap-y-4 px-4",children:h.map(t=>(0,a.jsx)("li",{children:(0,a.jsxs)(r(),{href:t.href,className:"flex items-center gap-x-3 rounded-md px-3 py-2 text-sm font-medium ".concat(e===t.href?"bg-gray-800 text-white":"text-gray-400 hover:bg-gray-800 hover:text-white"),children:[(0,a.jsx)(t.icon,{className:"h-5 w-5","aria-hidden":"true"}),t.name]})},t.name))})}),(0,a.jsx)("div",{className:"border-t border-gray-800 p-4",children:(0,a.jsx)("div",{className:"flex items-center gap-x-3",children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-800"})})})]})}function g(e){let{children:t}=e;return(0,a.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[(0,a.jsx)(m,{}),(0,a.jsx)("main",{className:"flex-1 overflow-auto",children:(0,a.jsx)("div",{className:"p-6",children:t})})]})}},63796:(e,t,s)=>{Promise.resolve().then(s.bind(s,18495))}},e=>{e.O(0,[302,579,20,213,967,53,126,409,441,964,358],()=>e(e.s=63796)),_N_E=e.O()}]);