<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDR Site Monitor</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 60px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        .status {
            background: rgba(16, 185, 129, 0.2);
            border: 2px solid #10B981;
            padding: 15px 30px;
            border-radius: 10px;
            display: inline-block;
            margin-bottom: 30px;
        }
        .links {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .link {
            background: rgba(255, 255, 255, 0.2);
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .info {
            margin-top: 40px;
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 CDR Site Monitor</h1>
        <p class="subtitle">Unified Deployment Successful!</p>
        
        <div class="status">
            ✅ Frontend and API integrated successfully
        </div>
        
        <div class="links">
            <a href="/api/sites" class="link">📊 API Endpoints</a>
            <a href="/up" class="link">💚 Health Check</a>
            <a href="/failed_jobs" class="link">⚠️ Test Failed Jobs</a>
        </div>
        
        <div class="info">
            <p><strong>Deployment Features:</strong></p>
            <p>✓ Next.js Frontend Built and Integrated</p>
            <p>✓ Rails API Serving Both Frontend and API</p>
            <p>✓ Single Port Unified Service</p>
            <p>✓ Static Asset Serving Configured</p>
            <p>✓ Database Migrations and Seeding</p>
            <p>✓ Health Checks and Error Handling</p>
        </div>
    </div>
    
    <script>
        // Simple JavaScript to test functionality
        console.log('CDR Site Monitor - Unified Deployment Active');
        
        // Test API connectivity
        fetch('/api/sites')
            .then(response => response.json())
            .then(data => {
                console.log('API Test Successful:', data);
            })
            .catch(error => {
                console.log('API Test:', error.message);
            });
    </script>
</body>
</html>
