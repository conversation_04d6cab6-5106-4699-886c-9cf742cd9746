<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDR Site Monitor - Deployed</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
            margin: 0; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; min-height: 100vh; display: flex; align-items: center; justify-content: center;
        }
        .container { 
            text-align: center; background: rgba(255,255,255,0.1); padding: 60px; 
            border-radius: 20px; backdrop-filter: blur(10px); box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        h1 { font-size: 3rem; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .status { 
            background: rgba(16,185,129,0.2); border: 2px solid #10B981; 
            padding: 15px 30px; border-radius: 10px; display: inline-block; margin: 20px 0;
        }
        .links { display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; margin: 30px 0; }
        .link { 
            background: rgba(255,255,255,0.2); padding: 12px 24px; border-radius: 8px; 
            text-decoration: none; color: white; transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.3);
        }
        .link:hover { background: rgba(255,255,255,0.3); transform: translateY(-2px); }
        .info { margin-top: 40px; font-size: 0.9rem; opacity: 0.8; }
        .config { background: rgba(0,0,0,0.2); padding: 20px; border-radius: 10px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 CDR Site Monitor</h1>
        <div class="status">✅ Deployment Successful - Unified Service Active</div>
        
        <div class="config">
            <h3>Deployment Configuration</h3>
            <p><strong>Port:</strong> 3000</p>
            <p><strong>Environment:</strong> development</p>
            <p><strong>Database Migrated:</strong> y</p>
            <p><strong>Database Seeded:</strong> y</p>
        </div>
        
        <div class="links">
            <a href="/api/sites" class="link">📊 Sites API</a>
            <a href="/api/sites/1/failed_jobs" class="link">⚠️ Failed Jobs</a>
            <a href="/up" class="link">💚 Health Check</a>
            <a href="/failed_jobs" class="link">🧪 Test Data</a>
        </div>
        
        <div class="info">
            <p><strong>Unified Service Features:</strong></p>
            <p>✓ Single Rails server serving both frontend and API</p>
            <p>✓ API endpoints available at /api/*</p>
            <p>✓ Frontend routes handled by Rails</p>
            <p>✓ Failed Jobs monitoring integrated</p>
            <p>✓ Database configured and ready</p>
        </div>
        
        <p style="margin-top: 40px; font-size: 0.8rem; opacity: 0.7;">
            Deployed at Wed Jul 16 02:40:02 PM CAT 2025
        </p>
    </div>
    
    <script>
        console.log('CDR Site Monitor - Unified Deployment Active');
        
        // Test API connectivity
        fetch('/api/sites')
            .then(r => r.json())
            .then(data => {
                console.log('✅ API Test Successful:', data.length, 'sites found');
                document.querySelector('.status').innerHTML = 
                    '✅ Deployment Successful - API Active (' + data.length + ' sites)';
            })
            .catch(e => {
                console.log('❌ API Test Failed:', e.message);
                document.querySelector('.status').innerHTML = 
                    '⚠️ Deployment Partial - API Issues';
            });
    </script>
</body>
</html>
