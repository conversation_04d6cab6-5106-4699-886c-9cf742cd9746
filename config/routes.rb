Rails.application.routes.draw do
  namespace :api do
    resources :sites do
      resources :health_checks, only: [ :index, :create ]
      resources :alerts, only: [ :index, :create ]
    end

    resources :health_checks, only: [ :show, :index ]
    resources :alerts, only: [ :update, :destroy ]
  end
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Defines the root path route ("/")
  # root "posts#index"
end
