Rails.application.routes.draw do
  # API routes
  namespace :api do
    resources :sites do
      resources :health_checks, only: [ :index, :create ]
      resources :alerts, only: [ :index, :create ]
      get :failed_jobs, on: :member
    end

    resources :health_checks, only: [ :show, :index ]
    resources :alerts, only: [ :update, :destroy ]
  end

  # Test endpoint to simulate failed jobs from monitored sites
  get "/failed_jobs", to: "test_failed_jobs#show"

  # Health check endpoint
  get "up" => "rails/health#show", as: :rails_health_check

  # Serve Next.js static assets
  get "/_next/*path", to: proc { |env|
    path = env["PATH_INFO"].sub(/^\/_next\//, "")
    file_path = Rails.public_path.join("_next", path)

    if File.exist?(file_path)
      [ 200, { "Content-Type" => Rack::Mime.mime_type(File.extname(path)) }, [ File.read(file_path) ] ]
    else
      [ 404, {}, [ "Not Found" ] ]
    end
  }

  # Frontend routes - catch all non-API routes and serve the React app
  # This should be the last route to catch all unmatched routes
  get "*path", to: "frontend#index", constraints: lambda { |request|
    # Don't serve frontend for API routes, assets, or Rails admin routes
    !request.path.start_with?("/api", "/rails", "/assets", "/_next") &&
    !request.path.include?(".") # Skip requests for files with extensions
  }

  # Root route
  root "frontend#index"
end
