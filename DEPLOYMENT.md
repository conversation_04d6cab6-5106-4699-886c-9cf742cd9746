# CDR Site Monitor - Deployment Guide

This guide explains how to deploy the CDR Site Monitor application as a single unified service using the provided CI/CD pipeline scripts.

## Overview

The deployment process builds the Next.js frontend and integrates it with the Rails API to create a single unified service that serves both the frontend application and API endpoints from one Rails server process.

## Quick Start

### Automated Deployment

1. **Run the deployment script:**
```bash
./scripts/deploy.sh
```

2. **Follow the prompts:**
   - Enter the port for the Rails server (default: 3000)
   - Choose the Rails environment (default: production)
   - Confirm database migration and seeding options

3. **Access the application:**
   - Complete Application: http://localhost:[port]
   - API Endpoints: http://localhost:[port]/api

### Quick Start (Subsequent Runs)

If the application is already deployed:

```bash
./scripts/start.sh [port] [environment]
```

Examples:
```bash
./scripts/start.sh 3000 production
./scripts/start.sh 8080 development
```

## Deployment Process Details

### What the Deployment Script Does

1. **Prerequisites Check**: Verifies Node.js, npm, Ruby, Bundler, and Rails are installed
2. **Configuration**: Prompts for deployment settings (port, environment, database options)
3. **Backup**: Creates a backup of the current deployment
4. **Frontend Configuration**: Updates API URLs in the frontend code
5. **Frontend Build**: Builds the Next.js application for production with static export
6. **Rails Integration**: Copies frontend assets to Rails public directory
7. **Rails Configuration**: Sets up Rails to serve frontend routes
8. **Database Setup**: Runs migrations and seeding as requested
9. **Asset Compilation**: Precompiles Rails assets
10. **Health Check**: Verifies the application starts correctly
11. **Rollback**: Automatic rollback on failure

### File Structure After Deployment

```
stream_monitor/
├── public/
│   ├── index.html          # Next.js main page
│   ├── _next/              # Next.js static assets
│   └── assets/             # Rails assets
├── app/controllers/
│   └── frontend_controller.rb  # Serves frontend routes
├── config/routes.rb        # Updated with frontend routes
└── scripts/
    ├── deploy.sh           # Main deployment script
    ├── start.sh            # Quick start script
    └── test-deployment.sh  # Pre-deployment tests
```

## Configuration Files

### Next.js Configuration (`frontend/next.config.js`)

- Enables static export for deployment
- Configures asset optimization
- Sets up proper routing for Rails integration

### Rails Routes (`config/routes.rb`)

- API routes under `/api/*`
- Static asset serving for `/_next/*`
- Catch-all route for frontend application
- Root route serves the frontend

### Rails Production Config

- Enables static file serving from `public/`
- Configures proper cache headers
- Optimizes for single-server deployment

## Environment Variables

The deployment script sets these environment variables:

- `RAILS_ENV`: Rails environment (production/development)
- `PORT`: Server port number

## Database Configuration

### Development
- Uses SQLite database
- Automatic migrations and seeding

### Production
- Configurable database migrations
- Optional seeding
- Backup creation before changes

## Troubleshooting

### Common Issues

1. **Frontend not found error**:
   - Run `./scripts/deploy.sh` to build the frontend
   - Check that `public/index.html` exists

2. **API calls failing**:
   - Verify the API URL in `frontend/src/lib/api.ts`
   - Check that Rails server is running on the correct port

3. **Static assets not loading**:
   - Ensure `config/environments/production.rb` has `public_file_server.enabled = true`
   - Check that `_next/` directory exists in `public/`

4. **Database errors**:
   - Run `rails db:migrate` manually
   - Check database permissions

### Health Checks

The deployment script includes automatic health checks:

- Verifies Rails server starts successfully
- Tests API endpoint availability
- Confirms frontend assets are accessible

### Rollback

If deployment fails, the script automatically:

- Restores previous public assets
- Restores database backup
- Provides rollback status

## Manual Deployment

If you prefer manual deployment:

1. **Build frontend:**
```bash
cd frontend
npm run build:export
```

2. **Copy assets:**
```bash
cp -r frontend/out/* public/
```

3. **Configure Rails:**
```bash
# Ensure routes.rb includes frontend routes
# Ensure production.rb enables static file serving
```

4. **Start server:**
```bash
RAILS_ENV=production bundle exec rails server -p 3000
```

## Security Considerations

- Static file serving is enabled for production
- CSRF protection is disabled for frontend routes
- Proper headers are set for security
- Asset caching is configured

## Performance Optimization

- Frontend assets are pre-built and optimized
- Rails asset pipeline is used for CSS/JS
- Proper cache headers for static files
- Single server process reduces overhead

## Monitoring

After deployment, monitor:

- Server logs for errors
- Application performance
- Database connections
- Static asset serving

## Testing the Deployment

### Verify the Unified Service

1. **Start the application:**
```bash
./scripts/start.sh 3000 development
```

2. **Test API endpoints:**
```bash
curl http://localhost:3000/api/sites
curl http://localhost:3000/up
```

3. **Test frontend:**
- Open http://localhost:3000 in browser
- Verify static assets load correctly
- Test navigation between pages

4. **Test failed jobs feature:**
```bash
curl http://localhost:3000/api/sites/1/failed_jobs
```

### Deployment Verification Checklist

- [ ] Rails server starts without errors
- [ ] Frontend loads at root URL (/)
- [ ] API endpoints respond at /api/*
- [ ] Static assets serve correctly
- [ ] Database migrations completed
- [ ] Health check endpoint works (/up)
- [ ] Failed jobs feature functional
- [ ] All routes work with client-side navigation

## Support

For issues with deployment:

1. Run `./scripts/test-deployment.sh` to check prerequisites
2. Check the deployment logs for specific errors
3. Verify all configuration files are properly set up
4. Test individual components (frontend build, Rails server) separately

## Success Indicators

When deployment is successful, you should see:

- ✅ Single Rails server process serving both frontend and API
- ✅ Frontend accessible at http://localhost:[port]
- ✅ API accessible at http://localhost:[port]/api
- ✅ All Next.js routes working with proper fallback
- ✅ Database properly migrated and seeded
- ✅ Failed jobs feature integrated and functional
- ✅ Health checks passing
