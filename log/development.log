  [1m[35m (1.2ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" varchar NOT NULL PRIMARY KEY) /*application='StreamMonitor'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" varchar NOT NULL PRIMARY KEY, "value" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.2ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (1.8ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-07-15 08:15:49.009144', '2025-07-15 08:15:49.009148') RETURNING "key" /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
Migrating to CreateSites (20250715081257)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[35m (6.0ms)[0m  [1m[35mCREATE TABLE "sites" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "ip_address" varchar NOT NULL, "port" integer NOT NULL, "status" varchar DEFAULT 'unknown', "last_check_at" datetime(6), "response_time" float, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='StreamMonitor'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_sites_on_ip_address_and_port" ON "sites" ("ip_address", "port") /*application='StreamMonitor'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_sites_on_status" ON "sites" ("status") /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250715081257') RETURNING "version" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
Migrating to CreateHealthChecks (20250715081337)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[35m (3.1ms)[0m  [1m[35mCREATE TABLE "health_checks" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "site_id" integer NOT NULL, "status" varchar DEFAULT 'unknown' NOT NULL, "response_time" float DEFAULT 0.0, "jobs_done" integer DEFAULT 0, "jobs_pending" integer DEFAULT 0, "jobs_failed" integer DEFAULT 0, "jobs_queued" integer DEFAULT 0, "last_sync_at" datetime(6), "visitor_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_78217d6d99"
FOREIGN KEY ("site_id")
  REFERENCES "sites" ("id")
) /*application='StreamMonitor'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_health_checks_on_site_id" ON "health_checks" ("site_id") /*application='StreamMonitor'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_health_checks_on_status" ON "health_checks" ("status") /*application='StreamMonitor'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_health_checks_on_created_at" ON "health_checks" ("created_at") /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250715081337') RETURNING "version" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
Migrating to CreateAlerts (20250715081346)
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[35m (3.2ms)[0m  [1m[35mCREATE TABLE "alerts" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "site_id" integer NOT NULL, "alert_type" varchar NOT NULL, "email" varchar NOT NULL, "is_active" boolean DEFAULT 1, "last_sent_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_80074b944a"
FOREIGN KEY ("site_id")
  REFERENCES "sites" ("id")
) /*application='StreamMonitor'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_alerts_on_site_id" ON "alerts" ("site_id") /*application='StreamMonitor'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_alerts_on_site_id_and_alert_type" ON "alerts" ("site_id", "alert_type") /*application='StreamMonitor'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_alerts_on_is_active" ON "alerts" ("is_active") /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250715081346') RETURNING "version" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
