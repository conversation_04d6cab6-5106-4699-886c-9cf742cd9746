  [1m[35m (1.2ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" varchar NOT NULL PRIMARY KEY) /*application='StreamMonitor'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" varchar NOT NULL PRIMARY KEY, "value" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.2ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (1.8ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-07-15 08:15:49.009144', '2025-07-15 08:15:49.009148') RETURNING "key" /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
Migrating to CreateSites (20250715081257)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[35m (6.0ms)[0m  [1m[35mCREATE TABLE "sites" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "ip_address" varchar NOT NULL, "port" integer NOT NULL, "status" varchar DEFAULT 'unknown', "last_check_at" datetime(6), "response_time" float, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='StreamMonitor'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_sites_on_ip_address_and_port" ON "sites" ("ip_address", "port") /*application='StreamMonitor'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_sites_on_status" ON "sites" ("status") /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250715081257') RETURNING "version" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
Migrating to CreateHealthChecks (20250715081337)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[35m (3.1ms)[0m  [1m[35mCREATE TABLE "health_checks" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "site_id" integer NOT NULL, "status" varchar DEFAULT 'unknown' NOT NULL, "response_time" float DEFAULT 0.0, "jobs_done" integer DEFAULT 0, "jobs_pending" integer DEFAULT 0, "jobs_failed" integer DEFAULT 0, "jobs_queued" integer DEFAULT 0, "last_sync_at" datetime(6), "visitor_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_78217d6d99"
FOREIGN KEY ("site_id")
  REFERENCES "sites" ("id")
) /*application='StreamMonitor'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_health_checks_on_site_id" ON "health_checks" ("site_id") /*application='StreamMonitor'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_health_checks_on_status" ON "health_checks" ("status") /*application='StreamMonitor'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_health_checks_on_created_at" ON "health_checks" ("created_at") /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250715081337') RETURNING "version" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
Migrating to CreateAlerts (20250715081346)
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[35m (3.2ms)[0m  [1m[35mCREATE TABLE "alerts" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "site_id" integer NOT NULL, "alert_type" varchar NOT NULL, "email" varchar NOT NULL, "is_active" boolean DEFAULT 1, "last_sent_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_80074b944a"
FOREIGN KEY ("site_id")
  REFERENCES "sites" ("id")
) /*application='StreamMonitor'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_alerts_on_site_id" ON "alerts" ("site_id") /*application='StreamMonitor'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_alerts_on_site_id_and_alert_type" ON "alerts" ("site_id", "alert_type") /*application='StreamMonitor'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_alerts_on_is_active" ON "alerts" ("is_active") /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250715081346') RETURNING "version" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
Started GET "/api/sites" for ::1 at 2025-07-15 10:33:49 +0200
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
Processing by Api::SitesController#index as */*
  Parameters: {"site"=>{}}
  [1m[36mSite Load (0.4ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 30ms (Views: 0.4ms | ActiveRecord: 2.2ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:34:37 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.7ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 8ms (Views: 0.6ms | ActiveRecord: 0.7ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:34:38 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (1.6ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 6ms (Views: 0.7ms | ActiveRecord: 0.5ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:34:40 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.2ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 2ms (Views: 0.1ms | ActiveRecord: 0.1ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:34:44 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 2ms (Views: 0.1ms | ActiveRecord: 0.1ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:34:49 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.6ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 5ms (Views: 0.3ms | ActiveRecord: 0.5ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:34:50 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 1ms (Views: 0.1ms | ActiveRecord: 0.1ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:34:52 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.2ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 2ms (Views: 0.1ms | ActiveRecord: 0.2ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:34:56 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 1ms (Views: 0.1ms | ActiveRecord: 0.1ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:36:03 +0200
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (1.0ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 27ms (Views: 0.2ms | ActiveRecord: 1.5ms (1 query, 0 cached) | GC: 2.8ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:36:26 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.4ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 5ms (Views: 0.4ms | ActiveRecord: 0.3ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:36:30 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.5ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 4ms (Views: 0.4ms | ActiveRecord: 0.5ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:36:33 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.8ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 4ms (Views: 0.3ms | ActiveRecord: 0.7ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:37:33 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 2ms (Views: 0.1ms | ActiveRecord: 0.1ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:38:33 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.3ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 3ms (Views: 0.5ms | ActiveRecord: 0.3ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:39:04 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.2ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 3ms (Views: 0.3ms | ActiveRecord: 0.2ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:39:15 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.4ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 3ms (Views: 0.3ms | ActiveRecord: 0.3ms (1 query, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:39:18 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
Completed 200 OK in 3ms (Views: 0.3ms | ActiveRecord: 0.1ms (1 query, 0 cached) | GC: 0.0ms)


  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
  [1m[36mSite Load (0.3ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mSite Create (1.4ms)[0m  [1m[32mINSERT INTO "sites" ("name", "ip_address", "port", "status", "last_check_at", "response_time", "created_at", "updated_at") VALUES ('Production App', '*************', 3000, 'up', '2025-07-15 08:10:22.860860', 499.0, '2025-07-15 08:39:22.870988', '2025-07-15 08:39:22.870988') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (1.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mSite Create (1.3ms)[0m  [1m[32mINSERT INTO "sites" ("name", "ip_address", "port", "status", "last_check_at", "response_time", "created_at", "updated_at") VALUES ('Staging Server', '*************', 3000, 'up', '2025-07-15 07:51:22.876619', 191.0, '2025-07-15 08:39:22.877004', '2025-07-15 08:39:22.877004') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mSite Create (2.7ms)[0m  [1m[32mINSERT INTO "sites" ("name", "ip_address", "port", "status", "last_check_at", "response_time", "created_at", "updated_at") VALUES ('Development Server', '*************', 3000, 'down', '2025-07-15 08:21:22.881547', NULL, '2025-07-15 08:39:22.882189', '2025-07-15 08:39:22.882189') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mSite Create (2.3ms)[0m  [1m[32mINSERT INTO "sites" ("name", "ip_address", "port", "status", "last_check_at", "response_time", "created_at", "updated_at") VALUES ('QA Environment', '*************', 3000, 'unknown', '2025-07-15 07:47:22.887841', NULL, '2025-07-15 08:39:22.888349', '2025-07-15 08:39:22.888349') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mSite Create (1.3ms)[0m  [1m[32mINSERT INTO "sites" ("name", "ip_address", "port", "status", "last_check_at", "response_time", "created_at", "updated_at") VALUES ('Legacy System', '*************', 8080, 'up', '2025-07-15 07:53:22.894339', 442.0, '2025-07-15 08:39:22.894835', '2025-07-15 08:39:22.894835') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.2ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'up', 328.0, 86, 19, 5, 9, '2025-07-15 08:11:22.915693', 720, '2025-07-14 11:39:22.915750', '2025-07-15 08:39:22.931585') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.8ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'up', 187.0, 34, 20, 3, 20, '2025-07-15 07:26:22.935711', 597, '2025-07-14 21:39:22.935773', '2025-07-15 08:39:22.937154') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.5ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'down', 4930.0, 0, 17, 9, 27, NULL, 0, '2025-07-14 15:39:22.941709', '2025-07-15 08:39:22.942363') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.5ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'up', 118.0, 72, 13, 2, 14, '2025-07-15 07:31:22.948416', 552, '2025-07-14 22:39:22.948506', '2025-07-15 08:39:22.950729') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.4ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'up', 274.0, 50, 10, 1, 11, '2025-07-15 07:44:22.969838', 821, '2025-07-14 08:39:22.969906', '2025-07-15 08:39:22.970804') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (2.9ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'up', 98.0, 16, 1, 4, 30, '2025-07-15 06:54:22.976095', 708, '2025-07-14 18:39:22.976178', '2025-07-15 08:39:22.977557') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.3ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'up', 335.0, 72, 20, 4, 25, '2025-07-15 07:50:22.983649', 439, '2025-07-14 16:39:22.983675', '2025-07-15 08:39:22.984304') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (2.1ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'up', 274.0, 21, 9, 1, 19, '2025-07-15 08:14:22.989515', 303, '2025-07-14 14:39:22.989577', '2025-07-15 08:39:22.991174') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (2.0ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'up', 421.0, 82, 7, 5, 9, '2025-07-15 07:55:22.996434', 146, '2025-07-14 18:39:22.996495', '2025-07-15 08:39:22.997568') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.8ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'up', 227.0, 60, 12, 0, 21, '2025-07-15 08:23:23.003515', 569, '2025-07-14 10:39:23.003574', '2025-07-15 08:39:23.004393') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.6ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (2, 'up', 156.0, 79, 15, 3, 3, '2025-07-15 08:02:23.011509', 331, '2025-07-14 19:39:23.011565', '2025-07-15 08:39:23.012923') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.3ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (2, 'up', 61.0, 31, 7, 1, 3, '2025-07-15 07:03:23.017649', 996, '2025-07-15 07:39:23.017683', '2025-07-15 08:39:23.018598') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (3.0ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (2, 'up', 106.0, 32, 5, 5, 11, '2025-07-15 06:54:23.023132', 760, '2025-07-14 19:39:23.023163', '2025-07-15 08:39:23.025705') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (2.1ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (2, 'up', 391.0, 14, 11, 3, 8, '2025-07-15 07:20:23.033823', 172, '2025-07-14 22:39:23.033850', '2025-07-15 08:39:23.035020') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (3.1ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (2, 'down', 4163.0, 0, 20, 15, 2, NULL, 0, '2025-07-14 23:39:23.040840', '2025-07-15 08:39:23.041898') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.5ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (2, 'up', 493.0, 57, 19, 3, 18, '2025-07-15 07:32:23.048555', 338, '2025-07-14 15:39:23.048581', '2025-07-15 08:39:23.049485') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.6ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (2, 'up', 78.0, 18, 10, 0, 5, '2025-07-15 08:10:23.053428', 339, '2025-07-14 09:39:23.053463', '2025-07-15 08:39:23.054754') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (2.0ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (2, 'up', 312.0, 71, 16, 4, 28, '2025-07-15 08:19:23.059815', 616, '2025-07-15 05:39:23.059857', '2025-07-15 08:39:23.060903') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.5ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (2, 'down', 4736.0, 0, 15, 15, 11, NULL, 0, '2025-07-14 11:39:23.066947', '2025-07-15 08:39:23.068356') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.9ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (3, 'up', 216.0, 76, 12, 4, 15, '2025-07-15 07:05:23.075482', 400, '2025-07-14 20:39:23.075518', '2025-07-15 08:39:23.076613') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.1ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (3, 'down', 1387.0, 0, 10, 17, 19, NULL, 0, '2025-07-15 05:39:23.083521', '2025-07-15 08:39:23.084240') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.5ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (3, 'up', 349.0, 95, 5, 1, 29, '2025-07-15 08:21:23.088187', 477, '2025-07-15 00:39:23.088376', '2025-07-15 08:39:23.090830') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (3.3ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (3, 'down', 1594.0, 0, 8, 20, 23, NULL, 0, '2025-07-14 22:39:23.097371', '2025-07-15 08:39:23.098911') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.5ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (3, 'up', 283.0, 87, 16, 1, 29, '2025-07-15 07:37:23.106619', 123, '2025-07-15 03:39:23.106684', '2025-07-15 08:39:23.107872') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.3ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (3, 'up', 229.0, 56, 9, 3, 19, '2025-07-15 07:19:23.113967', 733, '2025-07-14 10:39:23.114010', '2025-07-15 08:39:23.115112') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.4ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (4, 'down', 1873.0, 0, 5, 20, 28, NULL, 0, '2025-07-14 23:39:23.119336', '2025-07-15 08:39:23.120703') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (2.1ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (4, 'down', 4662.0, 0, 17, 6, 9, NULL, 0, '2025-07-15 06:39:23.125318', '2025-07-15 08:39:23.127344') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.9ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (4, 'up', 287.0, 61, 18, 2, 17, '2025-07-15 07:29:23.132470', 751, '2025-07-15 07:39:23.132498', '2025-07-15 08:39:23.133339') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.6ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (4, 'up', 495.0, 69, 14, 2, 12, '2025-07-15 07:13:23.139011', 264, '2025-07-14 21:39:23.139080', '2025-07-15 08:39:23.140691') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.4ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (4, 'down', 4941.0, 0, 2, 16, 21, NULL, 0, '2025-07-14 13:39:23.146197', '2025-07-15 08:39:23.147839') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.3ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (4, 'up', 204.0, 31, 20, 3, 23, '2025-07-15 07:56:23.152109', 441, '2025-07-15 06:39:23.152147', '2025-07-15 08:39:23.153094') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.7ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (5, 'up', 493.0, 61, 15, 1, 4, '2025-07-15 07:27:23.157783', 575, '2025-07-15 03:39:23.157848', '2025-07-15 08:39:23.159605') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (3.6ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (5, 'up', 253.0, 27, 17, 2, 25, '2025-07-15 08:20:23.164166', 931, '2025-07-14 20:39:23.164191', '2025-07-15 08:39:23.165086') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.4ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (5, 'up', 265.0, 30, 14, 5, 5, '2025-07-15 07:18:23.172192', 74, '2025-07-15 07:39:23.172233', '2025-07-15 08:39:23.173208') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (2.1ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (5, 'down', 1166.0, 0, 0, 17, 0, NULL, 0, '2025-07-15 07:39:23.179360', '2025-07-15 08:39:23.180181') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (3.0ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (5, 'up', 388.0, 70, 6, 1, 27, '2025-07-15 07:39:23.187349', 627, '2025-07-14 18:39:23.187413', '2025-07-15 08:39:23.189189') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Create (1.8ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (5, 'up', 357.0, 24, 15, 4, 16, '2025-07-15 07:46:23.196213', 230, '2025-07-14 10:39:23.196290', '2025-07-15 08:39:23.197249') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mAlert Create (1.6ms)[0m  [1m[32mINSERT INTO "alerts" ("site_id", "alert_type", "email", "is_active", "last_sent_at", "created_at", "updated_at") VALUES (1, 'performance', '<EMAIL>', 1, '2025-07-14 20:39:23.212154', '2025-07-15 08:39:23.221659', '2025-07-15 08:39:23.221659') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mAlert Create (1.8ms)[0m  [1m[32mINSERT INTO "alerts" ("site_id", "alert_type", "email", "is_active", "last_sent_at", "created_at", "updated_at") VALUES (2, 'performance', '<EMAIL>', 1, NULL, '2025-07-15 08:39:23.227408', '2025-07-15 08:39:23.227408') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mAlert Create (1.0ms)[0m  [1m[32mINSERT INTO "alerts" ("site_id", "alert_type", "email", "is_active", "last_sent_at", "created_at", "updated_at") VALUES (2, 'downtime', '<EMAIL>', 0, NULL, '2025-07-15 08:39:23.234454', '2025-07-15 08:39:23.234454') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mAlert Create (1.7ms)[0m  [1m[32mINSERT INTO "alerts" ("site_id", "alert_type", "email", "is_active", "last_sent_at", "created_at", "updated_at") VALUES (3, 'performance', '<EMAIL>', 0, '2025-07-14 00:39:23.237593', '2025-07-15 08:39:23.238885', '2025-07-15 08:39:23.238885') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mAlert Create (1.6ms)[0m  [1m[32mINSERT INTO "alerts" ("site_id", "alert_type", "email", "is_active", "last_sent_at", "created_at", "updated_at") VALUES (4, 'performance', '<EMAIL>', 0, NULL, '2025-07-15 08:39:23.247638', '2025-07-15 08:39:23.247638') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mAlert Create (2.3ms)[0m  [1m[32mINSERT INTO "alerts" ("site_id", "alert_type", "email", "is_active", "last_sent_at", "created_at", "updated_at") VALUES (4, 'performance', '<EMAIL>', 1, '2025-07-14 07:39:23.251258', '2025-07-15 08:39:23.251978', '2025-07-15 08:39:23.251978') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mAlert Create (1.5ms)[0m  [1m[32mINSERT INTO "alerts" ("site_id", "alert_type", "email", "is_active", "last_sent_at", "created_at", "updated_at") VALUES (5, 'downtime', '<EMAIL>', 0, NULL, '2025-07-15 08:39:23.260812', '2025-07-15 08:39:23.260812') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mAlert Create (1.6ms)[0m  [1m[32mINSERT INTO "alerts" ("site_id", "alert_type", "email", "is_active", "last_sent_at", "created_at", "updated_at") VALUES (5, 'downtime', '<EMAIL>', 1, '2025-07-14 09:39:23.265430', '2025-07-15 08:39:23.266148', '2025-07-15 08:39:23.266148') RETURNING "id" /*application='StreamMonitor'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  [1m[36mAlert Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "alerts" /*application='StreamMonitor'*/[0m
Started GET "/api/sites" for ::1 at 2025-07-15 10:40:29 +0200
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='StreamMonitor'*/[0m
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (1.3ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.6ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" IN (1, 2, 3, 4, 5) /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 1 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 2 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 2 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 3 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 3 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 4 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 4 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 5 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 5 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
Completed 200 OK in 94ms (Views: 0.7ms | ActiveRecord: 6.6ms (12 queries, 0 cached) | GC: 2.8ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:40:32 +0200
Processing by Api::SitesController#index as */*
  Parameters: {"site"=>{}}
  [1m[36mSite Load (0.2ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" IN (1, 2, 3, 4, 5) /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.5ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 1 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 2 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 2 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 3 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 3 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 4 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 4 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 5 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 5 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
Completed 200 OK in 27ms (Views: 0.1ms | ActiveRecord: 3.1ms (12 queries, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:40:45 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.7ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.7ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" IN (1, 2, 3, 4, 5) /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.4ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 1 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 2 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 2 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.8ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 3 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 3 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 4 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 4 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.6ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 5 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 5 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
Completed 200 OK in 44ms (Views: 0.4ms | ActiveRecord: 4.9ms (12 queries, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:40:54 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.4ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" IN (1, 2, 3, 4, 5) /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.8ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 1 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 2 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 2 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 3 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 3 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 4 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 4 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 5 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 5 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
Completed 200 OK in 41ms (Views: 0.7ms | ActiveRecord: 3.8ms (12 queries, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:41:14 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.2ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (1.1ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" IN (1, 2, 3, 4, 5) /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.6ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 1 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.4ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 2 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 2 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 3 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 3 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 4 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 4 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 5 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 5 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
Completed 200 OK in 34ms (Views: 0.2ms | ActiveRecord: 3.8ms (12 queries, 0 cached) | GC: 0.0ms)


  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" ORDER BY "sites"."id" ASC LIMIT 1 /*application='StreamMonitor'*/[0m
  ↳ test_alert.rb:7:in `<main>'
  [1m[36mAlert Load (0.2ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 1 ORDER BY "alerts"."id" ASC LIMIT 1 /*application='StreamMonitor'*/[0m
  ↳ test_alert.rb:14:in `<main>'
  Rendering layout layouts/mailer.html.erb
  Rendering alert_mailer/downtime_alert.html.erb within layouts/mailer
  Rendered alert_mailer/downtime_alert.html.erb within layouts/mailer (Duration: 0.8ms | GC: 0.0ms)
  Rendered layout layouts/mailer.html.erb (Duration: 1.4ms | GC: 0.0ms)
  Rendering layout layouts/mailer.text.erb
  Rendering alert_mailer/downtime_alert.text.erb within layouts/mailer
  Rendered alert_mailer/downtime_alert.text.erb within layouts/mailer (Duration: 0.3ms | GC: 0.0ms)
  Rendered layout layouts/mailer.text.erb (Duration: 0.5ms | GC: 0.0ms)
AlertMailer#downtime_alert: processed outbound mail in 62.3ms
<NAME_EMAIL> (26.8ms)
Date: Tue, 15 Jul 2025 10:47:39 +0200
From: <EMAIL>
To: <EMAIL>
Message-ID: <<EMAIL>>
Subject: =?UTF-8?Q?=F0=9F=9A=A8_Site_Down_Alert:_Production_App?=
Mime-Version: 1.0
Content-Type: multipart/alternative;
 boundary="--==_mimepart_687615ab6da1b_38f5ce9c9544c";
 charset=UTF-8
Content-Transfer-Encoding: 7bit


----==_mimepart_687615ab6da1b_38f5ce9c9544c
Content-Type: text/plain;
 charset=UTF-8
Content-Transfer-Encoding: 7bit

SITE DOWN ALERT - Stream Monitor
================================

Hello,

This is an automated alert from Stream Monitor.

SITE DOWN DETECTED
==================
Site: Production App
URL: http://*************:3000
Error: Connection timeout
Time: 2025-07-15 08:47:39 UTC

Please check your application immediately.

Last successful check: 2025-07-15 08:10:22 UTC

---
Stream Monitor
This is an automated message. Please do not reply.


----==_mimepart_687615ab6da1b_38f5ce9c9544c
Content-Type: text/html;
 charset=UTF-8
Content-Transfer-Encoding: quoted-printable

<!-- BEGIN app/views/layouts/mailer.html.erb --><!DOCTYPE html>
<html>
  <head>
    <meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3Dutf=
-8">
    <style>
      /* Email styles need to be inline */
    </style>
  </head>

  <body>
    <!-- BEGIN app/views/alert_mailer/downtime_alert.html.erb --><!DOCTYP=
E html>
<html>
<head>
  <meta content=3D'text/html; charset=3DUTF-8' http-equiv=3D'Content-Type=
' />
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
    }
    .header {
      background-color: #f44336;
      color: white;
      padding: 15px;
      text-align: center;
      border-radius: 5px 5px 0 0;
    }
    .content {
      padding: 20px;
      border: 1px solid #ddd;
      border-top: none;
      border-radius: 0 0 5px 5px;
    }
    .alert-details {
      background-color: #f9f9f9;
      padding: 15px;
      margin: 15px 0;
      border-left: 4px solid #f44336;
    }
    .footer {
      margin-top: 20px;
      font-size: 12px;
      color: #777;
      border-top: 1px solid #ddd;
      padding-top: 10px;
    }
  </style>
</head>
<body>
  <div class=3D"header">
    <h1>=F0=9F=9A=A8 Site Down Alert</h1>
  </div>

  <div class=3D"content">
    <p>Hello,</p>

    <p>This is an automated alert from Stream Monitor.</p>

    <div class=3D"alert-details">
      <h2>Site Down Detected</h2>
      <p><strong>Site:</strong> Production App</p>
      <p><strong>URL:</strong> http://*************:3000</p>
      <p><strong>Error:</strong> Connection timeout</p>
      <p><strong>Time:</strong> 2025-07-15 08:47:39 UTC</p>
    </div>

    <p>Please check your application immediately.</p>

    <p><strong>Last successful check:</strong> 2025-07-15 08:10:22 UTC</p=
>

    <div class=3D"footer">
      <p>Stream Monitor</p>
      <p>This is an automated message. Please do not reply.</p>
    </div>
  </div>
</body>
</html>
<!-- END app/views/alert_mailer/downtime_alert.html.erb -->
  </body>
</html>
<!-- END app/views/layouts/mailer.html.erb -->=

----==_mimepart_687615ab6da1b_38f5ce9c9544c--

  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  ↳ app/models/alert.rb:20:in `mark_as_sent!'
  [1m[36mAlert Update (1.1ms)[0m  [1m[33mUPDATE "alerts" SET "last_sent_at" = '2025-07-15 08:47:39.477521', "updated_at" = '2025-07-15 08:47:39.478292' WHERE "alerts"."id" = 1 /*application='StreamMonitor'*/[0m
  ↳ app/models/alert.rb:20:in `mark_as_sent!'
  [1m[36mTRANSACTION (1.8ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  ↳ app/models/alert.rb:20:in `mark_as_sent!'
Downtime alert <NAME_EMAIL> for site Production App
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  ↳ test_alert.rb:35:in `<main>'
  [1m[36mHealthCheck Create (1.1ms)[0m  [1m[32mINSERT INTO "health_checks" ("site_id", "status", "response_time", "jobs_done", "jobs_pending", "jobs_failed", "jobs_queued", "last_sync_at", "visitor_count", "created_at", "updated_at") VALUES (1, 'up', 6000.0, 50, 10, 2, 5, '2025-07-15 08:47:39.488369', 100, '2025-07-15 08:47:39.496892', '2025-07-15 08:47:39.496892') RETURNING "id" /*application='StreamMonitor'*/[0m
  ↳ test_alert.rb:35:in `<main>'
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  ↳ test_alert.rb:35:in `<main>'
  [1m[36mAlert Load (0.3ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 1 AND "alerts"."alert_type" = 'performance' LIMIT 1 /*application='StreamMonitor'*/[0m
  ↳ test_alert.rb:47:in `<main>'
  Rendering layout layouts/mailer.html.erb
  Rendering alert_mailer/performance_alert.html.erb within layouts/mailer
  Rendered alert_mailer/performance_alert.html.erb within layouts/mailer (Duration: 0.8ms | GC: 0.0ms)
  Rendered layout layouts/mailer.html.erb (Duration: 1.5ms | GC: 0.0ms)
  Rendering layout layouts/mailer.text.erb
  Rendering alert_mailer/performance_alert.text.erb within layouts/mailer
  Rendered alert_mailer/performance_alert.text.erb within layouts/mailer (Duration: 0.8ms | GC: 0.0ms)
  Rendered layout layouts/mailer.text.erb (Duration: 1.4ms | GC: 0.0ms)
AlertMailer#performance_alert: processed outbound mail in 7.8ms
<NAME_EMAIL> (2.8ms)
Date: Tue, 15 Jul 2025 10:47:39 +0200
From: <EMAIL>
To: <EMAIL>
Message-ID: <<EMAIL>>
Subject: =?UTF-8?Q?=E2=9A=A0=EF=B8=8F_Performance_Alert:_Production_App?=
Mime-Version: 1.0
Content-Type: multipart/alternative;
 boundary="--==_mimepart_687615ab7cf16_38f5ce9c956e7";
 charset=UTF-8
Content-Transfer-Encoding: 7bit


----==_mimepart_687615ab7cf16_38f5ce9c956e7
Content-Type: text/plain;
 charset=UTF-8
Content-Transfer-Encoding: 7bit

PERFORMANCE ALERT - Stream Monitor
==================================

Hello,

This is an automated alert from Stream Monitor.

PERFORMANCE ISSUE DETECTED
==========================
Site: Production App
URL: http://*************:3000
Response Time: 6000.0ms (threshold: 5000ms)
Time: 2025-07-15 08:47:39 UTC

CURRENT METRICS
===============
Jobs Done: 50
Jobs Pending: 10
Jobs Failed: 2
Jobs Queued: 5
Visitor Count: 100
Last Sync: 2025-07-15 08:47:39 UTC

Please investigate the performance issue.

---
Stream Monitor
This is an automated message. Please do not reply.


----==_mimepart_687615ab7cf16_38f5ce9c956e7
Content-Type: text/html;
 charset=UTF-8
Content-Transfer-Encoding: 7bit

<!-- BEGIN app/views/layouts/mailer.html.erb --><!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style>
      /* Email styles need to be inline */
    </style>
  </head>

  <body>
    <!-- BEGIN app/views/alert_mailer/performance_alert.html.erb --><h1>Alert#performance_alert</h1>

<p>
  , find me in app/views/alert_mailer/performance_alert.html.erb
</p>
<!-- END app/views/alert_mailer/performance_alert.html.erb -->
  </body>
</html>
<!-- END app/views/layouts/mailer.html.erb -->
----==_mimepart_687615ab7cf16_38f5ce9c956e7--

  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='StreamMonitor'*/[0m
  ↳ app/models/alert.rb:20:in `mark_as_sent!'
  [1m[36mAlert Update (1.3ms)[0m  [1m[33mUPDATE "alerts" SET "last_sent_at" = '2025-07-15 08:47:39.516151', "updated_at" = '2025-07-15 08:47:39.516686' WHERE "alerts"."id" = 1 /*application='StreamMonitor'*/[0m
  ↳ app/models/alert.rb:20:in `mark_as_sent!'
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='StreamMonitor'*/[0m
  ↳ app/models/alert.rb:20:in `mark_as_sent!'
Performance alert <NAME_EMAIL> for site Production App
Started GET "/api/sites" for ::1 at 2025-07-15 10:52:32 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.9ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.5ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" IN (1, 2, 3, 4, 5) /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 1 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 2 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 2 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 3 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 3 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.4ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 4 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 4 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 5 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 5 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
Completed 200 OK in 49ms (Views: 1.2ms | ActiveRecord: 3.4ms (12 queries, 0 cached) | GC: 2.2ms)


Started GET "/api/sites/1/alerts" for ::1 at 2025-07-15 10:52:41 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"1"}
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 1 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.2ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 17ms (Views: 9.0ms | ActiveRecord: 1.1ms (2 queries, 0 cached) | GC: 0.4ms)


Started GET "/api/sites/2/alerts" for ::1 at 2025-07-15 10:52:43 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"2"}
  [1m[36mSite Load (0.2ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 2 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.3ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 2 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 5ms (Views: 2.6ms | ActiveRecord: 0.4ms (2 queries, 0 cached) | GC: 0.0ms)


Started GET "/api/sites/3/alerts" for ::1 at 2025-07-15 10:52:45 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"3"}
  [1m[36mSite Load (0.2ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 3 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.1ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 3 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 4ms (Views: 2.3ms | ActiveRecord: 0.3ms (2 queries, 0 cached) | GC: 0.0ms)


Started GET "/api/sites/5/alerts" for ::1 at 2025-07-15 10:52:45 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"5"}
  [1m[36mSite Load (0.3ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 5 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.4ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 5 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 6ms (Views: 2.5ms | ActiveRecord: 0.7ms (2 queries, 0 cached) | GC: 0.0ms)


Started GET "/api/sites/4/alerts" for ::1 at 2025-07-15 10:52:47 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"4"}
  [1m[36mSite Load (0.2ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 4 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.2ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 4 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 8ms (Views: 6.3ms | ActiveRecord: 0.4ms (2 queries, 0 cached) | GC: 3.8ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:52:59 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (6.7ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.8ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" IN (1, 2, 3, 4, 5) /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 1 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 2 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 2 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 3 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 3 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 4 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 4 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 5 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 5 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
Completed 200 OK in 78ms (Views: 0.3ms | ActiveRecord: 9.7ms (12 queries, 0 cached) | GC: 41.7ms)


Started GET "/api/sites/5/alerts" for ::1 at 2025-07-15 10:53:00 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"5"}
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 5 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.2ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 5 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 7ms (Views: 5.1ms | ActiveRecord: 0.3ms (2 queries, 0 cached) | GC: 1.6ms)


Started GET "/api/sites/4/alerts" for ::1 at 2025-07-15 10:53:02 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"4"}
  [1m[36mSite Load (0.3ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 4 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.3ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 4 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 6ms (Views: 3.1ms | ActiveRecord: 0.6ms (2 queries, 0 cached) | GC: 0.0ms)


Started GET "/api/sites/3/alerts" for ::1 at 2025-07-15 10:53:03 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"3"}
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 3 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.1ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 3 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 3ms (Views: 1.6ms | ActiveRecord: 0.2ms (2 queries, 0 cached) | GC: 0.0ms)


Started GET "/api/sites/2/alerts" for ::1 at 2025-07-15 10:53:04 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"2"}
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 2 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.1ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 2 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 3ms (Views: 1.6ms | ActiveRecord: 0.2ms (2 queries, 0 cached) | GC: 0.0ms)


Started GET "/api/sites/1/alerts" for ::1 at 2025-07-15 10:53:04 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"1"}
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 1 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.1ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 4ms (Views: 2.6ms | ActiveRecord: 0.2ms (2 queries, 0 cached) | GC: 0.2ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:53:36 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.1ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" IN (1, 2, 3, 4, 5) /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 1 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.7ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 2 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 2 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.6ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 3 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 3 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.5ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 4 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 4 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.8ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 5 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 5 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
Completed 200 OK in 47ms (Views: 0.5ms | ActiveRecord: 4.1ms (12 queries, 0 cached) | GC: 1.3ms)


Started GET "/api/sites/1/alerts" for ::1 at 2025-07-15 10:53:37 +0200
Processing by Api::AlertsController#index as HTML
  Parameters: {"site_id"=>"1"}
  [1m[36mSite Load (0.3ms)[0m  [1m[34mSELECT "sites".* FROM "sites" WHERE "sites"."id" = 1 LIMIT 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:36:in `set_site'
  [1m[36mAlert Load (0.2ms)[0m  [1m[34mSELECT "alerts".* FROM "alerts" WHERE "alerts"."site_id" = 1 /*action='index',application='StreamMonitor',controller='alerts'*/[0m
  ↳ app/controllers/api/alerts_controller.rb:7:in `index'
Completed 200 OK in 4ms (Views: 1.4ms | ActiveRecord: 0.4ms (2 queries, 0 cached) | GC: 0.0ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:55:07 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.2ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.8ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" IN (1, 2, 3, 4, 5) /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 1 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.4ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 2 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 2 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.4ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 3 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 3 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.7ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 4 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 4 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.4ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 5 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 5 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
Completed 200 OK in 39ms (Views: 0.4ms | ActiveRecord: 4.1ms (12 queries, 0 cached) | GC: 0.6ms)


Started GET "/api/sites" for ::1 at 2025-07-15 10:55:12 +0200
Processing by Api::SitesController#index as HTML
  [1m[36mSite Load (0.3ms)[0m  [1m[34mSELECT "sites".* FROM "sites" /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.3ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" IN (1, 2, 3, 4, 5) /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:6:in `map'
  [1m[36mHealthCheck Load (0.7ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 1 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.2ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 2 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 2 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.4ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 3 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 3 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.6ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 4 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 4 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
  [1m[36mHealthCheck Load (0.6ms)[0m  [1m[34mSELECT "health_checks".* FROM "health_checks" WHERE "health_checks"."site_id" = 5 ORDER BY "health_checks"."created_at" DESC LIMIT 1 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/models/site.rb:23:in `latest_health_check'
  [1m[36mHealthCheck Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "health_checks" WHERE "health_checks"."site_id" = 5 /*action='index',application='StreamMonitor',controller='sites'*/[0m
  ↳ app/controllers/api/sites_controller.rb:49:in `site_with_latest_health_check'
Completed 200 OK in 45ms (Views: 0.3ms | ActiveRecord: 4.3ms (12 queries, 0 cached) | GC: 1.9ms)


