# CDR Site Monitor

A comprehensive monitoring application built with Rails and Next.js in a monolith architecture. Monitor multiple Rails applications deployed across different sites with real-time health checks, performance metrics, and email alerts.

## Features

### 🔍 Site Monitoring
- Add and manage multiple sites by IP address and port
- Real-time health status monitoring (up/down/unknown)
- Response time tracking
- Automatic periodic health checks

### 📊 Health Metrics
- Solid Queue job statistics (done, pending, failed, queued)
- **Failed Jobs Viewer** - Detailed inspection of failed Solid Queue jobs with error messages, retry counts, and job arguments
- Visitor count tracking since last synchronization
- Application synchronization timestamps
- Historical health check data

### 🚨 Alert System
- Email notifications for downtime events
- Performance alerts for slow response times
- Configurable alert types (downtime/performance)
- Rate limiting to prevent spam (15-minute cooldown)

### 📱 Modern Dashboard
- Real-time monitoring dashboard with date range filtering
- Detailed site views with interactive charts and refresh functionality
- **Failed Jobs Viewer** - Click-to-view detailed failed job information when jobs_failed > 0
- Site management interface
- Alert configuration panel
- Analytics dashboard with comprehensive insights
- Responsive design with Tailwind CSS

## Architecture

### Backend (Rails 8.0.2)
- **API-only Rails application** serving JSON endpoints
- **SQLite database** for development and testing
- **Solid Queue** for background job processing
- **Action Mailer** for email notifications
- **CORS enabled** for frontend communication

### Frontend (Next.js 15.4.1)
- **React with TypeScript** for type safety
- **Tailwind CSS** for styling
- **React Query** for data fetching and caching
- **Axios** for HTTP client
- **Lucide React** for icons
- **Recharts** for interactive data visualization
- **React DatePicker** for date range filtering

### Key Components
- **HealthCheckService**: Performs HTTP health checks on monitored sites
- **AlertService**: Handles email notification logic
- **HealthCheckJob**: Background job for periodic monitoring
- **AlertMailer**: Rails mailer for formatted email alerts

## Quick Start

### Prerequisites
- Ruby 3.2.0+
- Node.js 18+
- Rails 8.0.2+

### Installation

1. **Clone and setup the Rails backend:**
```bash
cd stream_monitor
bundle install
rails db:migrate
rails db:seed  # Creates sample data
```

2. **Setup the Next.js frontend:**
```bash
cd frontend
npm install
```

3. **Start the development servers:**

Terminal 1 (Rails API):
```bash
rails server -p 3000
```

Terminal 2 (Next.js Frontend):
```bash
cd frontend
npm run dev  # Runs on port 3001
```

4. **Access the application:**
- Frontend Dashboard: http://localhost:3001
- Rails API: http://localhost:3000/api

## Production Deployment (Unified Service)

For production deployment, use the unified deployment script that builds the Next.js frontend and integrates it with the Rails API to create a single service:

### Automated Deployment

1. **Run the deployment script:**
```bash
./scripts/deploy.sh
```

The script will:
- Prompt for configuration (port, environment, database options)
- Build the Next.js frontend for production
- Configure the frontend to use the correct API URL
- Integrate frontend assets with Rails
- Set up database migrations and seeding
- Perform health checks
- Start the unified application

2. **Quick start (if already deployed):**
```bash
# Use the start script for subsequent runs
./scripts/start.sh [port] [environment]

# Examples:
./scripts/start.sh 3000 production
./scripts/start.sh 8080 development
```

3. **Access the unified application:**
- Complete Application: http://localhost:[port]
- API Endpoints: http://localhost:[port]/api

### Manual Deployment Steps

If you prefer manual deployment:

1. **Configure frontend API URL:**
```bash
# Edit frontend/src/lib/api.ts to point to your production API URL
```

2. **Build frontend:**
```bash
cd frontend
npm run build:export
```

3. **Copy frontend assets to Rails:**
```bash
cp -r frontend/out/* public/
```

4. **Start Rails server:**
```bash
RAILS_ENV=production bundle exec rails server -p 3000
```

## Configuration

### Email Settings
Development emails are saved to `tmp/mail/` directory. For production, configure SMTP in `config/environments/production.rb`.

### Health Check Frequency
Modify `config/recurring.yml` to adjust monitoring intervals:
- Development: Every 2 minutes
- Production: Every 1 minute

### Alert Thresholds
- **Performance alerts**: Triggered when response time > 5000ms
- **Downtime alerts**: Triggered on connection failures or HTTP errors

## API Endpoints

### Failed Jobs API
The application includes a new API endpoint for retrieving detailed failed job information:

```
GET /api/sites/:site_id/failed_jobs
```

**Response Format:**
```json
{
  "site_id": 13,
  "site_name": "Staging Server",
  "failed_jobs": [
    {
      "id": 1,
      "job_class": "EmailDeliveryJob",
      "error_message": "Net::SMTPAuthenticationError: 535 Authentication failed",
      "failed_at": "2025-07-16T04:42:36Z",
      "retry_count": 3,
      "queue_name": "mailers",
      "arguments": {...},
      "priority": 0,
      "scheduled_at": "2025-07-16T03:42:36Z",
      "active_job_id": "abc123-def456-ghi789"
    }
  ],
  "total_count": 1
}
```

### Monitored Site Requirements
For the failed jobs feature to work, monitored sites should implement a `/failed_jobs` endpoint that returns failed Solid Queue job data. See `docs/health_endpoint_template.rb` for implementation details.
