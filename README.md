# Stream Monitor

A comprehensive monitoring application built with Rails and Next.js in a monolith architecture. Monitor multiple Rails applications deployed across different sites with real-time health checks, performance metrics, and email alerts.

## Features

### 🔍 Site Monitoring
- Add and manage multiple sites by IP address and port
- Real-time health status monitoring (up/down/unknown)
- Response time tracking
- Automatic periodic health checks

### 📊 Health Metrics
- Solid Queue job statistics (done, pending, failed, queued)
- Visitor count tracking since last synchronization
- Application synchronization timestamps
- Historical health check data

### 🚨 Alert System
- Email notifications for downtime events
- Performance alerts for slow response times
- Configurable alert types (downtime/performance)
- Rate limiting to prevent spam (15-minute cooldown)

### 📱 Modern Dashboard
- Real-time monitoring dashboard
- Site management interface
- Alert configuration panel
- Responsive design with Tailwind CSS

## Architecture

### Backend (Rails 8.0.2)
- **API-only Rails application** serving JSON endpoints
- **SQLite database** for development and testing
- **Solid Queue** for background job processing
- **Action Mailer** for email notifications
- **CORS enabled** for frontend communication

### Frontend (Next.js 15.4.1)
- **React with TypeScript** for type safety
- **Tailwind CSS** for styling
- **React Query** for data fetching and caching
- **Axios** for HTTP client
- **Lucide React** for icons

### Key Components
- **HealthCheckService**: Performs HTTP health checks on monitored sites
- **AlertService**: Handles email notification logic
- **HealthCheckJob**: Background job for periodic monitoring
- **AlertMailer**: Rails mailer for formatted email alerts

## Quick Start

### Prerequisites
- Ruby 3.2.0+
- Node.js 18+
- Rails 8.0.2+

### Installation

1. **Clone and setup the Rails backend:**
```bash
cd stream_monitor
bundle install
rails db:migrate
rails db:seed  # Creates sample data
```

2. **Setup the Next.js frontend:**
```bash
cd frontend
npm install
```

3. **Start the development servers:**

Terminal 1 (Rails API):
```bash
rails server -p 3000
```

Terminal 2 (Next.js Frontend):
```bash
cd frontend
npm run dev  # Runs on port 3001
```

4. **Access the application:**
- Frontend Dashboard: http://localhost:3001
- Rails API: http://localhost:3000/api

## Configuration

### Email Settings
Development emails are saved to `tmp/mail/` directory. For production, configure SMTP in `config/environments/production.rb`.

### Health Check Frequency
Modify `config/recurring.yml` to adjust monitoring intervals:
- Development: Every 2 minutes
- Production: Every 1 minute

### Alert Thresholds
- **Performance alerts**: Triggered when response time > 5000ms
- **Downtime alerts**: Triggered on connection failures or HTTP errors
