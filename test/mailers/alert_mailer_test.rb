require "test_helper"

class AlertMailerTest < ActionMailer::TestCase
  test "downtime_alert" do
    mail = AlertMailer.downtime_alert
    assert_equal "Downtime alert", mail.subject
    assert_equal [ "<EMAIL>" ], mail.to
    assert_equal [ "<EMAIL>" ], mail.from
    assert_match "Hi", mail.body.encoded
  end

  test "performance_alert" do
    mail = AlertMailer.performance_alert
    assert_equal "Performance alert", mail.subject
    assert_equal [ "<EMAIL>" ], mail.to
    assert_equal [ "<EMAIL>" ], mail.from
    assert_match "Hi", mail.body.encoded
  end
end
