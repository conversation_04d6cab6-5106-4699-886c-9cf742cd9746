/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for deployment
  output: 'export',
  
  // Disable image optimization for static export
  images: {
    unoptimized: true
  },
  
  // Configure trailing slash behavior
  trailingSlash: true,
  
  // Configure asset prefix for production builds
  assetPrefix: process.env.NODE_ENV === 'production' ? '' : '',
  
  // Configure base path if needed
  basePath: '',
  
  // Disable server-side features that don't work with static export
  experimental: {
    // Disable features that require server-side rendering
  },
  
  // Configure webpack for better builds
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Custom webpack configuration if needed
    return config;
  },
  
  // Configure redirects and rewrites
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: '/api/:path*', // Keep API routes as-is
      },
    ];
  },
  
  // Configure headers
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
