/* [project]/node_modules/react-datepicker/dist/react-datepicker.css [app-client] (css) */
.react-datepicker__navigation-icon:before, .react-datepicker__year-read-view--down-arrow, .react-datepicker__month-read-view--down-arrow, .react-datepicker__month-year-read-view--down-arrow {
  content: "";
  border: 3px solid #ccc;
  border-width: 3px 3px 0 0;
  width: 9px;
  height: 9px;
  display: block;
  position: absolute;
  top: 6px;
}

.react-datepicker-wrapper {
  border: 0;
  padding: 0;
  display: inline-block;
}

.react-datepicker {
  color: #000;
  font-family: Helvetica Neue, helvetica, arial, sans-serif;
  font-size: .8rem;
  line-height: initial;
  background-color: #fff;
  border: 1px solid #aeaeae;
  border-radius: .3rem;
  display: inline-block;
  position: relative;
}

.react-datepicker--time-only .react-datepicker__time-container {
  border-left: 0;
}

.react-datepicker--time-only .react-datepicker__time, .react-datepicker--time-only .react-datepicker__time-box {
  border-bottom-right-radius: .3rem;
  border-bottom-left-radius: .3rem;
}

.react-datepicker-popper {
  z-index: 1;
  line-height: 0;
}

.react-datepicker-popper .react-datepicker__triangle {
  stroke: #aeaeae;
}

.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
  fill: #f0f0f0;
  color: #f0f0f0;
}

.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle {
  fill: #fff;
  color: #fff;
}

.react-datepicker__header {
  text-align: center;
  background-color: #f0f0f0;
  border-bottom: 1px solid #aeaeae;
  border-top-left-radius: .3rem;
  padding: 8px 0;
  position: relative;
}

.react-datepicker__header--time {
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

.react-datepicker__header--time:not(.react-datepicker__header--time--only) {
  border-top-left-radius: 0;
}

.react-datepicker__header:not(.react-datepicker__header--has-time-select) {
  border-top-right-radius: .3rem;
}

.react-datepicker__year-dropdown-container--select, .react-datepicker__month-dropdown-container--select, .react-datepicker__month-year-dropdown-container--select, .react-datepicker__year-dropdown-container--scroll, .react-datepicker__month-dropdown-container--scroll, .react-datepicker__month-year-dropdown-container--scroll {
  margin: 0 15px;
  display: inline-block;
}

.react-datepicker__current-month, .react-datepicker-time__header, .react-datepicker-year-header {
  color: #000;
  margin-top: 0;
  font-size: .944rem;
  font-weight: bold;
}

h2.react-datepicker__current-month {
  margin: 0;
  padding: 0;
}

.react-datepicker-time__header {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.react-datepicker__navigation {
  text-align: center;
  cursor: pointer;
  z-index: 1;
  text-indent: -999em;
  background: none;
  border: none;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  position: absolute;
  top: 2px;
  overflow: hidden;
}

.react-datepicker__navigation--previous {
  left: 2px;
}

.react-datepicker__navigation--next {
  right: 2px;
}

.react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button) {
  right: 85px;
}

.react-datepicker__navigation--years {
  margin-left: auto;
  margin-right: auto;
  display: block;
  position: relative;
  top: 0;
}

.react-datepicker__navigation--years-previous {
  top: 4px;
}

.react-datepicker__navigation--years-upcoming {
  top: -4px;
}

.react-datepicker__navigation:hover :before {
  border-color: #a6a6a6;
}

.react-datepicker__navigation-icon {
  width: 0;
  font-size: 20px;
  position: relative;
  top: -1px;
}

.react-datepicker__navigation-icon--next {
  left: -2px;
}

.react-datepicker__navigation-icon--next:before {
  left: -7px;
  transform: rotate(45deg);
}

.react-datepicker__navigation-icon--previous {
  right: -2px;
}

.react-datepicker__navigation-icon--previous:before {
  right: -7px;
  transform: rotate(225deg);
}

.react-datepicker__month-container {
  float: left;
}

.react-datepicker__year {
  text-align: center;
  margin: .4rem;
}

.react-datepicker__year-wrapper {
  flex-wrap: wrap;
  max-width: 180px;
  display: flex;
}

.react-datepicker__year .react-datepicker__year-text {
  width: 4rem;
  margin: 2px;
  display: inline-block;
}

.react-datepicker__month {
  text-align: center;
  margin: .4rem;
}

.react-datepicker__month .react-datepicker__month-text, .react-datepicker__month .react-datepicker__quarter-text {
  width: 4rem;
  margin: 2px;
  display: inline-block;
}

.react-datepicker__input-time-container {
  clear: both;
  float: left;
  text-align: left;
  width: 100%;
  margin: 5px 0 10px 15px;
}

.react-datepicker__input-time-container .react-datepicker-time__caption, .react-datepicker__input-time-container .react-datepicker-time__input-container {
  display: inline-block;
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input {
  margin-left: 10px;
  display: inline-block;
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input {
  width: auto;
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type="time"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type="time"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type="time"] {
  -moz-appearance: textfield;
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__delimiter {
  margin-left: 5px;
  display: inline-block;
}

.react-datepicker__time-container {
  float: right;
  border-left: 1px solid #aeaeae;
  width: 85px;
}

.react-datepicker__time-container--with-today-button {
  border: 1px solid #aeaeae;
  border-radius: .3rem;
  display: inline;
  position: absolute;
  top: 0;
  right: -87px;
}

.react-datepicker__time-container .react-datepicker__time {
  background: #fff;
  border-bottom-right-radius: .3rem;
  position: relative;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box {
  text-align: center;
  border-bottom-right-radius: .3rem;
  width: 85px;
  margin: 0 auto;
  overflow-x: hidden;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list {
  box-sizing: content-box;
  width: 100%;
  height: calc(195px + .85rem);
  margin: 0;
  padding-left: 0;
  padding-right: 0;
  list-style: none;
  overflow-y: scroll;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
  white-space: nowrap;
  height: 30px;
  padding: 5px 10px;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
  cursor: pointer;
  background-color: #f0f0f0;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
  color: #fff;
  background-color: #216ba5;
  font-weight: bold;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover {
  background-color: #216ba5;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled {
  color: #ccc;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled:hover {
  cursor: default;
  background-color: rgba(0, 0, 0, 0);
}

.react-datepicker__week-number {
  color: #ccc;
  text-align: center;
  width: 1.7rem;
  margin: .166rem;
  line-height: 1.7rem;
  display: inline-block;
}

.react-datepicker__week-number.react-datepicker__week-number--clickable {
  cursor: pointer;
}

.react-datepicker__week-number.react-datepicker__week-number--clickable:not(.react-datepicker__week-number--selected):hover {
  background-color: #f0f0f0;
  border-radius: .3rem;
}

.react-datepicker__week-number--selected {
  color: #fff;
  background-color: #216ba5;
  border-radius: .3rem;
}

.react-datepicker__week-number--selected:hover {
  background-color: #1d5d90;
}

.react-datepicker__day-names {
  white-space: nowrap;
  margin-bottom: -8px;
}

.react-datepicker__week {
  white-space: nowrap;
}

.react-datepicker__day-name, .react-datepicker__day, .react-datepicker__time-name {
  color: #000;
  text-align: center;
  width: 1.7rem;
  margin: .166rem;
  line-height: 1.7rem;
  display: inline-block;
}

.react-datepicker__day, .react-datepicker__month-text, .react-datepicker__quarter-text, .react-datepicker__year-text {
  cursor: pointer;
}

.react-datepicker__day:not([aria-disabled="true"]):hover, .react-datepicker__month-text:not([aria-disabled="true"]):hover, .react-datepicker__quarter-text:not([aria-disabled="true"]):hover, .react-datepicker__year-text:not([aria-disabled="true"]):hover {
  background-color: #f0f0f0;
  border-radius: .3rem;
}

.react-datepicker__day--today, .react-datepicker__month-text--today, .react-datepicker__quarter-text--today, .react-datepicker__year-text--today {
  font-weight: bold;
}

.react-datepicker__day--highlighted, .react-datepicker__month-text--highlighted, .react-datepicker__quarter-text--highlighted, .react-datepicker__year-text--highlighted {
  color: #fff;
  background-color: #3dcc4a;
  border-radius: .3rem;
}

.react-datepicker__day--highlighted:not([aria-disabled="true"]):hover, .react-datepicker__month-text--highlighted:not([aria-disabled="true"]):hover, .react-datepicker__quarter-text--highlighted:not([aria-disabled="true"]):hover, .react-datepicker__year-text--highlighted:not([aria-disabled="true"]):hover {
  background-color: #32be3f;
}

.react-datepicker__day--highlighted-custom-1, .react-datepicker__month-text--highlighted-custom-1, .react-datepicker__quarter-text--highlighted-custom-1, .react-datepicker__year-text--highlighted-custom-1 {
  color: #f0f;
}

.react-datepicker__day--highlighted-custom-2, .react-datepicker__month-text--highlighted-custom-2, .react-datepicker__quarter-text--highlighted-custom-2, .react-datepicker__year-text--highlighted-custom-2 {
  color: green;
}

.react-datepicker__day--holidays, .react-datepicker__month-text--holidays, .react-datepicker__quarter-text--holidays, .react-datepicker__year-text--holidays {
  color: #fff;
  background-color: #ff6803;
  border-radius: .3rem;
  position: relative;
}

.react-datepicker__day--holidays .overlay, .react-datepicker__month-text--holidays .overlay, .react-datepicker__quarter-text--holidays .overlay, .react-datepicker__year-text--holidays .overlay {
  color: #fff;
  white-space: nowrap;
  visibility: hidden;
  opacity: 0;
  background-color: #333;
  border-radius: 4px;
  padding: 4px;
  transition: visibility, opacity .3s ease-in-out;
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.react-datepicker__day--holidays:not([aria-disabled="true"]):hover, .react-datepicker__month-text--holidays:not([aria-disabled="true"]):hover, .react-datepicker__quarter-text--holidays:not([aria-disabled="true"]):hover, .react-datepicker__year-text--holidays:not([aria-disabled="true"]):hover {
  background-color: #cf5300;
}

.react-datepicker__day--holidays:hover .overlay, .react-datepicker__month-text--holidays:hover .overlay, .react-datepicker__quarter-text--holidays:hover .overlay, .react-datepicker__year-text--holidays:hover .overlay {
  visibility: visible;
  opacity: 1;
}

.react-datepicker__day--selected, .react-datepicker__day--in-selecting-range, .react-datepicker__day--in-range, .react-datepicker__month-text--selected, .react-datepicker__month-text--in-selecting-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--selected, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--selected, .react-datepicker__year-text--in-selecting-range, .react-datepicker__year-text--in-range {
  color: #fff;
  background-color: #216ba5;
  border-radius: .3rem;
}

.react-datepicker__day--selected:not([aria-disabled="true"]):hover, .react-datepicker__day--in-selecting-range:not([aria-disabled="true"]):hover, .react-datepicker__day--in-range:not([aria-disabled="true"]):hover, .react-datepicker__month-text--selected:not([aria-disabled="true"]):hover, .react-datepicker__month-text--in-selecting-range:not([aria-disabled="true"]):hover, .react-datepicker__month-text--in-range:not([aria-disabled="true"]):hover, .react-datepicker__quarter-text--selected:not([aria-disabled="true"]):hover, .react-datepicker__quarter-text--in-selecting-range:not([aria-disabled="true"]):hover, .react-datepicker__quarter-text--in-range:not([aria-disabled="true"]):hover, .react-datepicker__year-text--selected:not([aria-disabled="true"]):hover, .react-datepicker__year-text--in-selecting-range:not([aria-disabled="true"]):hover, .react-datepicker__year-text--in-range:not([aria-disabled="true"]):hover {
  background-color: #1d5d90;
}

.react-datepicker__day--keyboard-selected, .react-datepicker__month-text--keyboard-selected, .react-datepicker__quarter-text--keyboard-selected, .react-datepicker__year-text--keyboard-selected {
  color: #000;
  background-color: #bad9f1;
  border-radius: .3rem;
}

.react-datepicker__day--keyboard-selected:not([aria-disabled="true"]):hover, .react-datepicker__month-text--keyboard-selected:not([aria-disabled="true"]):hover, .react-datepicker__quarter-text--keyboard-selected:not([aria-disabled="true"]):hover, .react-datepicker__year-text--keyboard-selected:not([aria-disabled="true"]):hover {
  background-color: #1d5d90;
}

.react-datepicker__day--in-selecting-range:not(:-webkit-any(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)), .react-datepicker__month-text--in-selecting-range:not(:-webkit-any(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)), .react-datepicker__quarter-text--in-selecting-range:not(:-webkit-any(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)), .react-datepicker__year-text--in-selecting-range:not(:-webkit-any(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)) {
  background-color: rgba(33, 107, 165, .5);
}

.react-datepicker__day--in-selecting-range:not(:-moz-any(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)), .react-datepicker__month-text--in-selecting-range:not(:-moz-any(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)), .react-datepicker__quarter-text--in-selecting-range:not(:-moz-any(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)), .react-datepicker__year-text--in-selecting-range:not(:-moz-any(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)) {
  background-color: rgba(33, 107, 165, .5);
}

.react-datepicker__day--in-selecting-range:not(:is(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)), .react-datepicker__month-text--in-selecting-range:not(:is(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)), .react-datepicker__quarter-text--in-selecting-range:not(:is(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)), .react-datepicker__year-text--in-selecting-range:not(:is(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range)) {
  background-color: rgba(33, 107, 165, .5);
}

.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(:-webkit-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__day--in-range:not(:-webkit-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(:-webkit-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__month-text--in-range:not(:-webkit-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(:-webkit-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__quarter-text--in-range:not(:-webkit-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(:-webkit-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__year-text--in-range:not(:-webkit-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)) {
  color: #000;
  background-color: #f0f0f0;
}

.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(:-moz-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__day--in-range:not(:-moz-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(:-moz-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__month-text--in-range:not(:-moz-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(:-moz-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__quarter-text--in-range:not(:-moz-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(:-moz-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__year-text--in-range:not(:-moz-any(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)) {
  color: #000;
  background-color: #f0f0f0;
}

.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(:is(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__day--in-range:not(:is(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(:is(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__month-text--in-range:not(:is(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(:is(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__quarter-text--in-range:not(:is(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(:is(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)), .react-datepicker__year--selecting-range .react-datepicker__year-text--in-range:not(:is(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range)) {
  color: #000;
  background-color: #f0f0f0;
}

.react-datepicker__day--disabled, .react-datepicker__month-text--disabled, .react-datepicker__quarter-text--disabled, .react-datepicker__year-text--disabled {
  cursor: default;
  color: #ccc;
}

.react-datepicker__day--disabled .overlay, .react-datepicker__month-text--disabled .overlay, .react-datepicker__quarter-text--disabled .overlay, .react-datepicker__year-text--disabled .overlay {
  color: #fff;
  white-space: nowrap;
  visibility: hidden;
  opacity: 0;
  background-color: #333;
  border-radius: 4px;
  padding: 4px;
  transition: visibility, opacity .3s ease-in-out;
  position: absolute;
  bottom: 70%;
  left: 50%;
  transform: translateX(-50%);
}

.react-datepicker__input-container {
  width: 100%;
  display: inline-block;
  position: relative;
}

.react-datepicker__input-container .react-datepicker__calendar-icon {
  box-sizing: content-box;
  padding: .5rem;
  position: absolute;
}

.react-datepicker__view-calendar-icon input {
  padding: 6px 10px 5px 25px;
}

.react-datepicker__year-read-view, .react-datepicker__month-read-view, .react-datepicker__month-year-read-view {
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: .3rem;
  position: relative;
}

.react-datepicker__year-read-view:hover, .react-datepicker__month-read-view:hover, .react-datepicker__month-year-read-view:hover {
  cursor: pointer;
}

.react-datepicker__year-read-view:hover .react-datepicker__year-read-view--down-arrow, .react-datepicker__year-read-view:hover .react-datepicker__month-read-view--down-arrow, .react-datepicker__month-read-view:hover .react-datepicker__year-read-view--down-arrow, .react-datepicker__month-read-view:hover .react-datepicker__month-read-view--down-arrow, .react-datepicker__month-year-read-view:hover .react-datepicker__year-read-view--down-arrow, .react-datepicker__month-year-read-view:hover .react-datepicker__month-read-view--down-arrow {
  border-top-color: #b3b3b3;
}

.react-datepicker__year-read-view--down-arrow, .react-datepicker__month-read-view--down-arrow, .react-datepicker__month-year-read-view--down-arrow {
  top: 0;
  right: -16px;
  transform: rotate(135deg);
}

.react-datepicker__year-dropdown, .react-datepicker__month-dropdown, .react-datepicker__month-year-dropdown {
  z-index: 1;
  text-align: center;
  background-color: #f0f0f0;
  border: 1px solid #aeaeae;
  border-radius: .3rem;
  width: 50%;
  position: absolute;
  top: 30px;
  left: 25%;
}

.react-datepicker__year-dropdown:hover, .react-datepicker__month-dropdown:hover, .react-datepicker__month-year-dropdown:hover {
  cursor: pointer;
}

.react-datepicker__year-dropdown--scrollable, .react-datepicker__month-dropdown--scrollable, .react-datepicker__month-year-dropdown--scrollable {
  height: 150px;
  overflow-y: scroll;
}

.react-datepicker__year-option, .react-datepicker__month-option, .react-datepicker__month-year-option {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  line-height: 20px;
  display: block;
}

.react-datepicker__year-option:first-of-type, .react-datepicker__month-option:first-of-type, .react-datepicker__month-year-option:first-of-type {
  border-top-left-radius: .3rem;
  border-top-right-radius: .3rem;
}

.react-datepicker__year-option:last-of-type, .react-datepicker__month-option:last-of-type, .react-datepicker__month-year-option:last-of-type {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border-bottom-right-radius: .3rem;
  border-bottom-left-radius: .3rem;
}

.react-datepicker__year-option:hover, .react-datepicker__month-option:hover, .react-datepicker__month-year-option:hover {
  background-color: #ccc;
}

.react-datepicker__year-option:hover .react-datepicker__navigation--years-upcoming, .react-datepicker__month-option:hover .react-datepicker__navigation--years-upcoming, .react-datepicker__month-year-option:hover .react-datepicker__navigation--years-upcoming {
  border-bottom-color: #b3b3b3;
}

.react-datepicker__year-option:hover .react-datepicker__navigation--years-previous, .react-datepicker__month-option:hover .react-datepicker__navigation--years-previous, .react-datepicker__month-year-option:hover .react-datepicker__navigation--years-previous {
  border-top-color: #b3b3b3;
}

.react-datepicker__year-option--selected, .react-datepicker__month-option--selected, .react-datepicker__month-year-option--selected {
  position: absolute;
  left: 15px;
}

.react-datepicker__close-icon {
  cursor: pointer;
  vertical-align: middle;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  outline: 0;
  height: 100%;
  padding: 0 6px 0 0;
  display: table-cell;
  position: absolute;
  top: 0;
  right: 0;
}

.react-datepicker__close-icon:after {
  cursor: pointer;
  color: #fff;
  text-align: center;
  vertical-align: middle;
  content: "×";
  background-color: #216ba5;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  padding: 2px;
  font-size: 12px;
  line-height: 1;
  display: table-cell;
}

.react-datepicker__close-icon--disabled {
  cursor: default;
}

.react-datepicker__close-icon--disabled:after {
  cursor: default;
  background-color: #ccc;
}

.react-datepicker__today-button {
  cursor: pointer;
  text-align: center;
  clear: left;
  background: #f0f0f0;
  border-top: 1px solid #aeaeae;
  padding: 5px 0;
  font-weight: bold;
}

.react-datepicker__portal {
  z-index: 2147483647;
  background-color: rgba(0, 0, 0, .8);
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
}

.react-datepicker__portal .react-datepicker__day-name, .react-datepicker__portal .react-datepicker__day, .react-datepicker__portal .react-datepicker__time-name {
  width: 3rem;
  line-height: 3rem;
}

@media (max-width: 400px), (max-height: 550px) {
  .react-datepicker__portal .react-datepicker__day-name, .react-datepicker__portal .react-datepicker__day, .react-datepicker__portal .react-datepicker__time-name {
    width: 2rem;
    line-height: 2rem;
  }
}

.react-datepicker__portal .react-datepicker__current-month, .react-datepicker__portal .react-datepicker-time__header {
  font-size: 1.44rem;
}

.react-datepicker__children-container {
  width: 13.8rem;
  height: auto;
  margin: .4rem;
  padding-left: .2rem;
  padding-right: .2rem;
}

.react-datepicker__aria-live {
  clip-path: circle(0);
  white-space: nowrap;
  border: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.react-datepicker__calendar-icon {
  vertical-align: -.125em;
  width: 1em;
  height: 1em;
}

/*# sourceMappingURL=node_modules_react-datepicker_dist_react-datepicker_a24da800.css.map*/