{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/stream_monitor/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance with base URL and default headers\nconst api = axios.create({\n  baseURL: 'http://localhost:3000/api',\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Types\nexport interface Site {\n  id: number;\n  name: string;\n  ip_address: string;\n  port: number;\n  status: 'up' | 'down' | 'unknown';\n  last_check_at: string | null;\n  response_time: number | null;\n  created_at: string;\n  updated_at: string;\n  latest_health_check?: HealthCheck;\n  health_checks_count?: number;\n}\n\nexport interface HealthCheck {\n  id: number;\n  site_id: number;\n  status: 'up' | 'down' | 'unknown';\n  response_time: number | null;\n  jobs_done: number | null;\n  jobs_pending: number | null;\n  jobs_failed: number | null;\n  jobs_queued: number | null;\n  last_sync_at: string | null;\n  visitor_count: number | null;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Alert {\n  id: number;\n  site_id: number;\n  alert_type: 'downtime' | 'performance';\n  email: string;\n  is_active: boolean;\n  last_sent_at: string | null;\n  created_at: string;\n  updated_at: string;\n}\n\n// API functions for Sites\nexport const sitesApi = {\n  getAll: async (): Promise<Site[]> => {\n    const response = await api.get('/sites');\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Site> => {\n    const response = await api.get(`/sites/${id}`);\n    return response.data;\n  },\n\n  create: async (site: Omit<Site, 'id' | 'created_at' | 'updated_at'>): Promise<Site> => {\n    const response = await api.post('/sites', { site });\n    return response.data;\n  },\n\n  update: async (id: number, site: Partial<Site>): Promise<Site> => {\n    const response = await api.put(`/sites/${id}`, { site });\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await api.delete(`/sites/${id}`);\n  },\n};\n\n// API functions for Health Checks\nexport const healthChecksApi = {\n  getBySiteId: async (siteId: number): Promise<HealthCheck[]> => {\n    const response = await api.get(`/sites/${siteId}/health_checks`);\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<HealthCheck> => {\n    const response = await api.get(`/health_checks/${id}`);\n    return response.data;\n  },\n\n  create: async (\n    siteId: number,\n    healthCheck: Omit<HealthCheck, 'id' | 'site_id' | 'created_at' | 'updated_at'>\n  ): Promise<HealthCheck> => {\n    const response = await api.post(`/sites/${siteId}/health_checks`, { health_check: healthCheck });\n    return response.data;\n  },\n};\n\n// API functions for Alerts\nexport const alertsApi = {\n  getBySiteId: async (siteId: number): Promise<Alert[]> => {\n    const response = await api.get(`/sites/${siteId}/alerts`);\n    return response.data;\n  },\n\n  create: async (\n    siteId: number,\n    alert: Omit<Alert, 'id' | 'site_id' | 'created_at' | 'updated_at'>\n  ): Promise<Alert> => {\n    const response = await api.post(`/sites/${siteId}/alerts`, { alert });\n    return response.data;\n  },\n\n  update: async (id: number, alert: Partial<Alert>): Promise<Alert> => {\n    const response = await api.put(`/alerts/${id}`, { alert });\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await api.delete(`/alerts/${id}`);\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,0DAA0D;AAC1D,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AA4CO,MAAM,WAAW;IACtB,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,UAAY,OAAH;QACzC,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,UAAU;YAAE;QAAK;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,UAAY,OAAH,KAAM;YAAE;QAAK;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,IAAI,MAAM,CAAC,AAAC,UAAY,OAAH;IAC7B;AACF;AAGO,MAAM,kBAAkB;IAC7B,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,UAAgB,OAAP,QAAO;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,kBAAoB,OAAH;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OACN,QACA;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,AAAC,UAAgB,OAAP,QAAO,mBAAiB;YAAE,cAAc;QAAY;QAC9F,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,UAAgB,OAAP,QAAO;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OACN,QACA;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,AAAC,UAAgB,OAAP,QAAO,YAAU;YAAE;QAAM;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH,KAAM;YAAE;QAAM;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,IAAI,MAAM,CAAC,AAAC,WAAa,OAAH;IAC9B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/stream_monitor/frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { BarChart3, Bell, Home, Server, Settings } from 'lucide-react';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: Home },\n  { name: 'Sites', href: '/sites', icon: Server },\n  { name: '<PERSON><PERSON><PERSON>', href: '/alerts', icon: Bell },\n  { name: 'Analytics', href: '/analytics', icon: BarChart3 },\n  { name: 'Settings', href: '/settings', icon: Settings },\n];\n\nexport default function Sidebar() {\n  const pathname = usePathname();\n\n  return (\n    <div className=\"flex h-full flex-col bg-gray-900 text-white w-64\">\n      <div className=\"flex h-16 shrink-0 items-center px-6 border-b border-gray-800\">\n        <h1 className=\"text-xl font-bold\">Stream Monitor</h1>\n      </div>\n      <nav className=\"flex flex-1 flex-col py-4\">\n        <ul className=\"flex flex-1 flex-col gap-y-4 px-4\">\n          {navigation.map((item) => (\n            <li key={item.name}>\n              <Link\n                href={item.href}\n                className={`flex items-center gap-x-3 rounded-md px-3 py-2 text-sm font-medium ${\n                  pathname === item.href\n                    ? 'bg-gray-800 text-white'\n                    : 'text-gray-400 hover:bg-gray-800 hover:text-white'\n                }`}\n              >\n                <item.icon className=\"h-5 w-5\" aria-hidden=\"true\" />\n                {item.name}\n              </Link>\n            </li>\n          ))}\n        </ul>\n      </nav>\n      <div className=\"border-t border-gray-800 p-4\">\n        <div className=\"flex items-center gap-x-3\">\n          <div className=\"h-8 w-8 rounded-full bg-gray-800\"></div>\n          <div>\n            <p className=\"text-sm font-medium text-white\">Admin User</p>\n            <p className=\"text-xs text-gray-400\"><EMAIL></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IAC3C;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,yMAAA,CAAA,SAAM;IAAC;IAC9C;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC9C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,qNAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACvD;AAEc,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAAoB;;;;;;;;;;;0BAEpC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;sCACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,AAAC,sEAIX,OAHC,aAAa,KAAK,IAAI,GAClB,2BACA;;kDAGN,6LAAC,KAAK,IAAI;wCAAC,WAAU;wCAAU,eAAY;;;;;;oCAC1C,KAAK,IAAI;;;;;;;2BAVL,KAAK,IAAI;;;;;;;;;;;;;;;0BAgBxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAiC;;;;;;8CAC9C,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;GAtCwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/stream_monitor/frontend/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Sidebar from './Sidebar';\n\ninterface DashboardLayoutProps {\n  children: ReactNode;\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      <Sidebar />\n      <main className=\"flex-1 overflow-auto\">\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AASe,SAAS,gBAAgB,KAAkC;QAAlC,EAAE,QAAQ,EAAwB,GAAlC;IACtC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BACR,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;KAXwB", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/stream_monitor/frontend/src/app/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useQuery } from '@tanstack/react-query';\nimport { sitesApi, healthChecksApi } from '@/lib/api';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport { \n  <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,\n  BarChart, Bar, PieChart, Pie, Cell\n} from 'recharts';\nimport { format, subDays, startOfDay } from 'date-fns';\nimport { TrendingUp, TrendingDown, Activity, AlertTriangle } from 'lucide-react';\n\nconst COLORS = ['#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];\n\nexport default function AnalyticsPage() {\n  const { data: sites = [], isLoading: sitesLoading } = useQuery({\n    queryKey: ['sites'],\n    queryFn: sitesApi.getAll,\n  });\n\n  // Get health checks for all sites\n  const healthCheckQueries = useQuery({\n    queryKey: ['allHealthChecks'],\n    queryFn: async () => {\n      const allHealthChecks = await Promise.all(\n        sites.map(site => healthChecksApi.getBySiteId(site.id))\n      );\n      return allHealthChecks.flat();\n    },\n    enabled: sites.length > 0,\n  });\n\n  if (sitesLoading || healthCheckQueries.isLoading) {\n    return (\n      <DashboardLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  const allHealthChecks = healthCheckQueries.data || [];\n\n  // Calculate analytics data\n  const last7Days = Array.from({ length: 7 }, (_, i) => {\n    const date = subDays(new Date(), 6 - i);\n    return startOfDay(date);\n  });\n\n  const dailyStats = last7Days.map(date => {\n    const dayStart = date;\n    const dayEnd = new Date(date.getTime() + 24 * 60 * 60 * 1000);\n    \n    const dayChecks = allHealthChecks.filter(check => {\n      const checkDate = new Date(check.created_at);\n      return checkDate >= dayStart && checkDate < dayEnd;\n    });\n\n    const upChecks = dayChecks.filter(check => check.status === 'up');\n    const downChecks = dayChecks.filter(check => check.status === 'down');\n    \n    return {\n      date: format(date, 'MMM dd'),\n      totalChecks: dayChecks.length,\n      upChecks: upChecks.length,\n      downChecks: downChecks.length,\n      avgResponseTime: upChecks.length > 0 \n        ? upChecks.reduce((sum, check) => sum + (check.response_time || 0), 0) / upChecks.length\n        : 0,\n      uptime: dayChecks.length > 0 ? (upChecks.length / dayChecks.length) * 100 : 0,\n    };\n  });\n\n  // Site status distribution\n  const statusDistribution = [\n    { name: 'Up', value: sites.filter(site => site.status === 'up').length, color: '#10B981' },\n    { name: 'Down', value: sites.filter(site => site.status === 'down').length, color: '#EF4444' },\n    { name: 'Unknown', value: sites.filter(site => site.status === 'unknown').length, color: '#F59E0B' },\n  ].filter(item => item.value > 0);\n\n  // Overall statistics\n  const totalChecks = allHealthChecks.length;\n  const upChecks = allHealthChecks.filter(check => check.status === 'up').length;\n  const overallUptime = totalChecks > 0 ? (upChecks / totalChecks) * 100 : 0;\n  const avgResponseTime = upChecks > 0 \n    ? allHealthChecks\n        .filter(check => check.status === 'up' && check.response_time)\n        .reduce((sum, check) => sum + (check.response_time || 0), 0) / upChecks\n    : 0;\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Analytics</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Comprehensive analytics and insights for your monitored sites\n          </p>\n        </div>\n\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <Activity className=\"h-6 w-6 text-blue-400\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Overall Uptime</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{overallUptime.toFixed(1)}%</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <TrendingUp className=\"h-6 w-6 text-green-400\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Avg Response Time</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{avgResponseTime.toFixed(0)}ms</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <AlertTriangle className=\"h-6 w-6 text-yellow-400\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Checks</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{totalChecks}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <TrendingDown className=\"h-6 w-6 text-red-400\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Failed Checks</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{totalChecks - upChecks}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Charts Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Uptime Trend */}\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">7-Day Uptime Trend</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={dailyStats}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"date\" />\n                <YAxis domain={[0, 100]} />\n                <Tooltip \n                  formatter={(value, name) => [`${Number(value).toFixed(1)}%`, 'Uptime']}\n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"uptime\" \n                  stroke=\"#10B981\" \n                  strokeWidth={3}\n                  dot={{ fill: '#10B981', strokeWidth: 2, r: 5 }}\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Site Status Distribution */}\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Site Status Distribution</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={statusDistribution}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={({ name, value }) => `${name}: ${value}`}\n                  outerRadius={100}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                >\n                  {statusDistribution.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Response Time Trend */}\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Average Response Time Trend</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={dailyStats}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"date\" />\n                <YAxis />\n                <Tooltip \n                  formatter={(value, name) => [`${Number(value).toFixed(0)}ms`, 'Avg Response Time']}\n                />\n                <Bar dataKey=\"avgResponseTime\" fill=\"#3B82F6\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Health Check Volume */}\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Daily Health Check Volume</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={dailyStats}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"date\" />\n                <YAxis />\n                <Tooltip />\n                <Bar dataKey=\"upChecks\" stackId=\"a\" fill=\"#10B981\" name=\"Successful\" />\n                <Bar dataKey=\"downChecks\" stackId=\"a\" fill=\"#EF4444\" name=\"Failed\" />\n                <Legend />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        {/* Summary Table */}\n        <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n          <div className=\"px-4 py-5 sm:px-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900\">Site Performance Summary</h3>\n          </div>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Site\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Health Checks\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Avg Response Time\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Last Check\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {sites.map((site) => {\n                  const siteChecks = allHealthChecks.filter(check => check.site_id === site.id);\n                  const siteUpChecks = siteChecks.filter(check => check.status === 'up');\n                  const siteAvgResponse = siteUpChecks.length > 0 \n                    ? siteUpChecks.reduce((sum, check) => sum + (check.response_time || 0), 0) / siteUpChecks.length\n                    : 0;\n\n                  return (\n                    <tr key={site.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">{site.name}</div>\n                        <div className=\"text-sm text-gray-500\">{site.ip_address}:{site.port}</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${\n                          site.status === 'up' ? 'bg-green-100 text-green-800' :\n                          site.status === 'down' ? 'bg-red-100 text-red-800' :\n                          'bg-yellow-100 text-yellow-800'\n                        }`}>\n                          {site.status}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {siteChecks.length}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {siteAvgResponse.toFixed(0)}ms\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {site.last_check_at \n                          ? format(new Date(site.last_check_at), 'MMM dd, HH:mm')\n                          : 'Never'\n                        }\n                      </td>\n                    </tr>\n                  );\n                })}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;AAYA,MAAM,SAAS;IAAC;IAAW;IAAW;IAAW;CAAU;AAE5C,SAAS;;IACtB,MAAM,EAAE,MAAM,QAAQ,EAAE,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC7D,UAAU;YAAC;SAAQ;QACnB,SAAS,oHAAA,CAAA,WAAQ,CAAC,MAAM;IAC1B;IAEA,kCAAkC;IAClC,MAAM,qBAAqB,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAClC,UAAU;YAAC;SAAkB;QAC7B,OAAO;0DAAE;gBACP,MAAM,kBAAkB,MAAM,QAAQ,GAAG,CACvC,MAAM,GAAG;kEAAC,CAAA,OAAQ,oHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,KAAK,EAAE;;gBAEvD,OAAO,gBAAgB,IAAI;YAC7B;;QACA,SAAS,MAAM,MAAM,GAAG;IAC1B;IAEA,IAAI,gBAAgB,mBAAmB,SAAS,EAAE;QAChD,qBACE,6LAAC,kJAAA,CAAA,UAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,MAAM,kBAAkB,mBAAmB,IAAI,IAAI,EAAE;IAErD,2BAA2B;IAC3B,MAAM,YAAY,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG;QAC9C,MAAM,OAAO,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ,IAAI;QACrC,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE;IACpB;IAEA,MAAM,aAAa,UAAU,GAAG,CAAC,CAAA;QAC/B,MAAM,WAAW;QACjB,MAAM,SAAS,IAAI,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK;QAExD,MAAM,YAAY,gBAAgB,MAAM,CAAC,CAAA;YACvC,MAAM,YAAY,IAAI,KAAK,MAAM,UAAU;YAC3C,OAAO,aAAa,YAAY,YAAY;QAC9C;QAEA,MAAM,WAAW,UAAU,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAC5D,MAAM,aAAa,UAAU,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAE9D,OAAO;YACL,MAAM,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YACnB,aAAa,UAAU,MAAM;YAC7B,UAAU,SAAS,MAAM;YACzB,YAAY,WAAW,MAAM;YAC7B,iBAAiB,SAAS,MAAM,GAAG,IAC/B,SAAS,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,aAAa,IAAI,CAAC,GAAG,KAAK,SAAS,MAAM,GACtF;YACJ,QAAQ,UAAU,MAAM,GAAG,IAAI,AAAC,SAAS,MAAM,GAAG,UAAU,MAAM,GAAI,MAAM;QAC9E;IACF;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB;QACzB;YAAE,MAAM;YAAM,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,MAAM,MAAM;YAAE,OAAO;QAAU;QACzF;YAAE,MAAM;YAAQ,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,MAAM;YAAE,OAAO;QAAU;QAC7F;YAAE,MAAM;YAAW,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;YAAE,OAAO;QAAU;KACpG,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG;IAE9B,qBAAqB;IACrB,MAAM,cAAc,gBAAgB,MAAM;IAC1C,MAAM,WAAW,gBAAgB,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,MAAM,MAAM;IAC9E,MAAM,gBAAgB,cAAc,IAAI,AAAC,WAAW,cAAe,MAAM;IACzE,MAAM,kBAAkB,WAAW,IAC/B,gBACG,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,QAAQ,MAAM,aAAa,EAC5D,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,aAAa,IAAI,CAAC,GAAG,KAAK,WACjE;IAEJ,qBACE,6LAAC,kJAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAM5C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;;4DAAqC,cAAc,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOtF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;;4DAAqC,gBAAgB,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAqC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7E,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wCAAC,MAAM;;0DACf,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;gDAAC,QAAQ;oDAAC;oDAAG;iDAAI;;;;;;0DACvB,6LAAC,0JAAA,CAAA,UAAO;gDACN,WAAW,CAAC,OAAO,OAAS;wDAAE,GAA2B,OAAzB,OAAO,OAAO,OAAO,CAAC,IAAG;wDAAI;qDAAS;;;;;;0DAExE,6LAAC,uJAAA,CAAA,OAAI;gDACH,MAAK;gDACL,SAAQ;gDACR,QAAO;gDACP,aAAa;gDACb,KAAK;oDAAE,MAAM;oDAAW,aAAa;oDAAG,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;;;;;sCAOrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;0DACP,6LAAC,kJAAA,CAAA,MAAG;gDACF,MAAM;gDACN,IAAG;gDACH,IAAG;gDACH,WAAW;gDACX,OAAO;wDAAC,EAAE,IAAI,EAAE,KAAK,EAAE;2DAAK,AAAC,GAAW,OAAT,MAAK,MAAU,OAAN;;gDACxC,aAAa;gDACb,MAAK;gDACL,SAAQ;0DAEP,mBAAmB,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,MAAM,KAAK;uDAAlC,AAAC,QAAa,OAAN;;;;;;;;;;0DAGvB,6LAAC,0JAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wCAAC,MAAM;;0DACd,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0DACN,6LAAC,0JAAA,CAAA,UAAO;gDACN,WAAW,CAAC,OAAO,OAAS;wDAAE,GAA2B,OAAzB,OAAO,OAAO,OAAO,CAAC,IAAG;wDAAK;qDAAoB;;;;;;0DAEpF,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAkB,MAAK;;;;;;;;;;;;;;;;;;;;;;;sCAM1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wCAAC,MAAM;;0DACd,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0DACN,6LAAC,0JAAA,CAAA,UAAO;;;;;0DACR,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAW,SAAQ;gDAAI,MAAK;gDAAU,MAAK;;;;;;0DACxD,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAa,SAAQ;gDAAI,MAAK;gDAAU,MAAK;;;;;;0DAC1D,6LAAC,yJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAA8C;;;;;;;;;;;sCAE9D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;;;;;;;;;;;;kDAKnG,6LAAC;wCAAM,WAAU;kDACd,MAAM,GAAG,CAAC,CAAC;4CACV,MAAM,aAAa,gBAAgB,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,KAAK,KAAK,EAAE;4CAC5E,MAAM,eAAe,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;4CACjE,MAAM,kBAAkB,aAAa,MAAM,GAAG,IAC1C,aAAa,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,aAAa,IAAI,CAAC,GAAG,KAAK,aAAa,MAAM,GAC9F;4CAEJ,qBACE,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;0EAAqC,KAAK,IAAI;;;;;;0EAC7D,6LAAC;gEAAI,WAAU;;oEAAyB,KAAK,UAAU;oEAAC;oEAAE,KAAK,IAAI;;;;;;;;;;;;;kEAErE,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAW,AAAC,iEAIjB,OAHC,KAAK,MAAM,KAAK,OAAO,gCACvB,KAAK,MAAM,KAAK,SAAS,4BACzB;sEAEC,KAAK,MAAM;;;;;;;;;;;kEAGhB,6LAAC;wDAAG,WAAU;kEACX,WAAW,MAAM;;;;;;kEAEpB,6LAAC;wDAAG,WAAU;;4DACX,gBAAgB,OAAO,CAAC;4DAAG;;;;;;;kEAE9B,6LAAC;wDAAG,WAAU;kEACX,KAAK,aAAa,GACf,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,aAAa,GAAG,mBACrC;;;;;;;+CAvBC,KAAK,EAAE;;;;;wCA4BpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GAlTwB;;QACgC,8KAAA,CAAA,WAAQ;QAMnC,8KAAA,CAAA,WAAQ;;;KAPb", "debugId": null}}]}