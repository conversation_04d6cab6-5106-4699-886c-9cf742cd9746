{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/stream_monitor/frontend/src/providers/QueryProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactNode, useState } from 'react';\n\ninterface QueryProviderProps {\n  children: ReactNode;\n}\n\nexport default function QueryProvider({ children }: QueryProviderProps) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            staleTime: 30 * 1000, // 30 seconds\n            refetchInterval: 60 * 1000, // 1 minute\n          },\n        },\n      })\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n    </QueryClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;AAHA;;;AASe,SAAS,cAAc,KAAgC;QAAhC,EAAE,QAAQ,EAAsB,GAAhC;;IACpC,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;kCAC3B,IACE,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACd,gBAAgB;oBACd,SAAS;wBACP,WAAW,KAAK;wBAChB,iBAAiB,KAAK;oBACxB;gBACF;YACF;;IAGJ,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC1B;;;;;;AAGP;GAlBwB;KAAA", "debugId": null}}]}