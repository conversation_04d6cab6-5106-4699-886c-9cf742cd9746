{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/stream_monitor/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance with base URL and default headers\nconst api = axios.create({\n  baseURL: 'http://localhost:3000/api',\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Types\nexport interface Site {\n  id: number;\n  name: string;\n  ip_address: string;\n  port: number;\n  status: 'up' | 'down' | 'unknown';\n  last_check_at: string | null;\n  response_time: number | null;\n  created_at: string;\n  updated_at: string;\n  latest_health_check?: HealthCheck;\n  health_checks_count?: number;\n}\n\nexport interface HealthCheck {\n  id: number;\n  site_id: number;\n  status: 'up' | 'down' | 'unknown';\n  response_time: number | null;\n  jobs_done: number | null;\n  jobs_pending: number | null;\n  jobs_failed: number | null;\n  jobs_queued: number | null;\n  last_sync_at: string | null;\n  visitor_count: number | null;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Alert {\n  id: number;\n  site_id: number;\n  alert_type: 'downtime' | 'performance';\n  email: string;\n  is_active: boolean;\n  last_sent_at: string | null;\n  created_at: string;\n  updated_at: string;\n}\n\n// API functions for Sites\nexport const sitesApi = {\n  getAll: async (): Promise<Site[]> => {\n    const response = await api.get('/sites');\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Site> => {\n    const response = await api.get(`/sites/${id}`);\n    return response.data;\n  },\n\n  create: async (site: Omit<Site, 'id' | 'created_at' | 'updated_at'>): Promise<Site> => {\n    const response = await api.post('/sites', { site });\n    return response.data;\n  },\n\n  update: async (id: number, site: Partial<Site>): Promise<Site> => {\n    const response = await api.put(`/sites/${id}`, { site });\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await api.delete(`/sites/${id}`);\n  },\n};\n\n// API functions for Health Checks\nexport const healthChecksApi = {\n  getBySiteId: async (siteId: number): Promise<HealthCheck[]> => {\n    const response = await api.get(`/sites/${siteId}/health_checks`);\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<HealthCheck> => {\n    const response = await api.get(`/health_checks/${id}`);\n    return response.data;\n  },\n\n  create: async (\n    siteId: number,\n    healthCheck: Omit<HealthCheck, 'id' | 'site_id' | 'created_at' | 'updated_at'>\n  ): Promise<HealthCheck> => {\n    const response = await api.post(`/sites/${siteId}/health_checks`, { health_check: healthCheck });\n    return response.data;\n  },\n};\n\n// API functions for Alerts\nexport const alertsApi = {\n  getBySiteId: async (siteId: number): Promise<Alert[]> => {\n    const response = await api.get(`/sites/${siteId}/alerts`);\n    return response.data;\n  },\n\n  create: async (\n    siteId: number,\n    alert: Omit<Alert, 'id' | 'site_id' | 'created_at' | 'updated_at'>\n  ): Promise<Alert> => {\n    const response = await api.post(`/sites/${siteId}/alerts`, { alert });\n    return response.data;\n  },\n\n  update: async (id: number, alert: Partial<Alert>): Promise<Alert> => {\n    const response = await api.put(`/alerts/${id}`, { alert });\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await api.delete(`/alerts/${id}`);\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,0DAA0D;AAC1D,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AA4CO,MAAM,WAAW;IACtB,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,UAAY,OAAH;QACzC,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,UAAU;YAAE;QAAK;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,UAAY,OAAH,KAAM;YAAE;QAAK;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,IAAI,MAAM,CAAC,AAAC,UAAY,OAAH;IAC7B;AACF;AAGO,MAAM,kBAAkB;IAC7B,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,UAAgB,OAAP,QAAO;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,kBAAoB,OAAH;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OACN,QACA;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,AAAC,UAAgB,OAAP,QAAO,mBAAiB;YAAE,cAAc;QAAY;QAC9F,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,UAAgB,OAAP,QAAO;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OACN,QACA;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,AAAC,UAAgB,OAAP,QAAO,YAAU;YAAE;QAAM;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH,KAAM;YAAE;QAAM;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,IAAI,MAAM,CAAC,AAAC,WAAa,OAAH;IAC9B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/stream_monitor/frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { BarChart3, Bell, Home, Server, Settings } from 'lucide-react';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: Home },\n  { name: 'Sites', href: '/sites', icon: Server },\n  { name: '<PERSON><PERSON><PERSON>', href: '/alerts', icon: Bell },\n  { name: 'Analytics', href: '/analytics', icon: BarChart3 },\n  { name: 'Settings', href: '/settings', icon: Settings },\n];\n\nexport default function Sidebar() {\n  const pathname = usePathname();\n\n  return (\n    <div className=\"flex h-full flex-col bg-gray-900 text-white w-64\">\n      <div className=\"flex h-16 shrink-0 items-center px-6 border-b border-gray-800\">\n        <h1 className=\"text-xl font-bold\">Stream Monitor</h1>\n      </div>\n      <nav className=\"flex flex-1 flex-col py-4\">\n        <ul className=\"flex flex-1 flex-col gap-y-4 px-4\">\n          {navigation.map((item) => (\n            <li key={item.name}>\n              <Link\n                href={item.href}\n                className={`flex items-center gap-x-3 rounded-md px-3 py-2 text-sm font-medium ${\n                  pathname === item.href\n                    ? 'bg-gray-800 text-white'\n                    : 'text-gray-400 hover:bg-gray-800 hover:text-white'\n                }`}\n              >\n                <item.icon className=\"h-5 w-5\" aria-hidden=\"true\" />\n                {item.name}\n              </Link>\n            </li>\n          ))}\n        </ul>\n      </nav>\n      <div className=\"border-t border-gray-800 p-4\">\n        <div className=\"flex items-center gap-x-3\">\n          <div className=\"h-8 w-8 rounded-full bg-gray-800\"></div>\n          <div>\n            <p className=\"text-sm font-medium text-white\">Admin User</p>\n            <p className=\"text-xs text-gray-400\"><EMAIL></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IAC3C;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,yMAAA,CAAA,SAAM;IAAC;IAC9C;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC9C;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,qNAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACvD;AAEc,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAAoB;;;;;;;;;;;0BAEpC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;sCACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,AAAC,sEAIX,OAHC,aAAa,KAAK,IAAI,GAClB,2BACA;;kDAGN,6LAAC,KAAK,IAAI;wCAAC,WAAU;wCAAU,eAAY;;;;;;oCAC1C,KAAK,IAAI;;;;;;;2BAVL,KAAK,IAAI;;;;;;;;;;;;;;;0BAgBxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAiC;;;;;;8CAC9C,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;GAtCwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/stream_monitor/frontend/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Sidebar from './Sidebar';\n\ninterface DashboardLayoutProps {\n  children: ReactNode;\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      <Sidebar />\n      <main className=\"flex-1 overflow-auto\">\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AASe,SAAS,gBAAgB,KAAkC;QAAlC,EAAE,QAAQ,EAAwB,GAAlC;IACtC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BACR,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;KAXwB", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/stream_monitor/frontend/src/app/sites/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useQuery } from '@tanstack/react-query';\nimport { useParams } from 'next/navigation';\nimport { sitesApi, healthChecksApi } from '@/lib/api';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport { \n  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,\n  AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell\n} from 'recharts';\nimport { format } from 'date-fns';\nimport { \n  ArrowLeft, Activity, Clock, TrendingUp, TrendingDown, \n  CheckCircle, AlertTriangle, Server, Users, Zap \n} from 'lucide-react';\nimport Link from 'next/link';\n\nconst COLORS = ['#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];\n\nexport default function SiteDetailPage() {\n  const params = useParams();\n  const siteId = parseInt(params.id as string);\n\n  const { data: site, isLoading: siteLoading } = useQuery({\n    queryKey: ['sites', siteId],\n    queryFn: () => sitesApi.getById(siteId),\n  });\n\n  const { data: healthChecks = [], isLoading: healthChecksLoading } = useQuery({\n    queryKey: ['healthChecks', siteId],\n    queryFn: () => healthChecksApi.getBySiteId(siteId),\n  });\n\n  if (siteLoading || healthChecksLoading) {\n    return (\n      <DashboardLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  if (!site) {\n    return (\n      <DashboardLayout>\n        <div className=\"text-center py-12\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Site not found</h3>\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  // Prepare chart data\n  const chartData = healthChecks\n    .slice(-20) // Last 20 checks\n    .map(check => ({\n      time: format(new Date(check.created_at), 'HH:mm'),\n      responseTime: check.response_time || 0,\n      status: check.status,\n      jobsDone: check.jobs_done || 0,\n      jobsPending: check.jobs_pending || 0,\n      jobsFailed: check.jobs_failed || 0,\n      jobsQueued: check.jobs_queued || 0,\n      visitorCount: check.visitor_count || 0,\n    }));\n\n  // Calculate statistics\n  const recentChecks = healthChecks.slice(-10);\n  const avgResponseTime = recentChecks.reduce((sum, check) => sum + (check.response_time || 0), 0) / recentChecks.length;\n  const uptime = (recentChecks.filter(check => check.status === 'up').length / recentChecks.length) * 100;\n  \n  const latestCheck = healthChecks[0];\n  const totalJobs = latestCheck ? \n    (latestCheck.jobs_done || 0) + (latestCheck.jobs_pending || 0) + \n    (latestCheck.jobs_failed || 0) + (latestCheck.jobs_queued || 0) : 0;\n\n  const jobsData = latestCheck ? [\n    { name: 'Done', value: latestCheck.jobs_done || 0, color: '#10B981' },\n    { name: 'Pending', value: latestCheck.jobs_pending || 0, color: '#F59E0B' },\n    { name: 'Failed', value: latestCheck.jobs_failed || 0, color: '#EF4444' },\n    { name: 'Queued', value: latestCheck.jobs_queued || 0, color: '#8B5CF6' },\n  ] : [];\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <Link \n              href=\"/sites\"\n              className=\"p-2 rounded-md hover:bg-gray-100\"\n            >\n              <ArrowLeft className=\"h-5 w-5\" />\n            </Link>\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">{site.name}</h1>\n              <p className=\"text-sm text-gray-600\">{site.ip_address}:{site.port}</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {site.status === 'up' && <CheckCircle className=\"h-6 w-6 text-green-500\" />}\n            {site.status === 'down' && <AlertTriangle className=\"h-6 w-6 text-red-500\" />}\n            {site.status === 'unknown' && <Clock className=\"h-6 w-6 text-yellow-500\" />}\n            <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n              site.status === 'up' ? 'bg-green-100 text-green-800' :\n              site.status === 'down' ? 'bg-red-100 text-red-800' :\n              'bg-yellow-100 text-yellow-800'\n            }`}>\n              {site.status.toUpperCase()}\n            </span>\n          </div>\n        </div>\n\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <Activity className=\"h-6 w-6 text-blue-400\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Uptime (Last 10)</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{uptime.toFixed(1)}%</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <Zap className=\"h-6 w-6 text-yellow-400\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Avg Response Time</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{avgResponseTime.toFixed(0)}ms</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <Server className=\"h-6 w-6 text-purple-400\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Jobs</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{totalJobs}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <Users className=\"h-6 w-6 text-green-400\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Visitors</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">{latestCheck?.visitor_count || 0}</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Charts Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Response Time Chart */}\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Response Time Trend</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={chartData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"time\" />\n                <YAxis />\n                <Tooltip \n                  formatter={(value, name) => [`${value}ms`, 'Response Time']}\n                  labelFormatter={(label) => `Time: ${label}`}\n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"responseTime\" \n                  stroke=\"#3B82F6\" \n                  strokeWidth={2}\n                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Jobs Distribution */}\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Current Jobs Distribution</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={jobsData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={({ name, value }) => `${name}: ${value}`}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                >\n                  {jobsData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Jobs Over Time */}\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Jobs Over Time</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <AreaChart data={chartData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"time\" />\n                <YAxis />\n                <Tooltip />\n                <Area type=\"monotone\" dataKey=\"jobsDone\" stackId=\"1\" stroke=\"#10B981\" fill=\"#10B981\" />\n                <Area type=\"monotone\" dataKey=\"jobsPending\" stackId=\"1\" stroke=\"#F59E0B\" fill=\"#F59E0B\" />\n                <Area type=\"monotone\" dataKey=\"jobsFailed\" stackId=\"1\" stroke=\"#EF4444\" fill=\"#EF4444\" />\n                <Area type=\"monotone\" dataKey=\"jobsQueued\" stackId=\"1\" stroke=\"#8B5CF6\" fill=\"#8B5CF6\" />\n              </AreaChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Visitor Count */}\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Visitor Count Trend</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={chartData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"time\" />\n                <YAxis />\n                <Tooltip />\n                <Bar dataKey=\"visitorCount\" fill=\"#10B981\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        {/* Recent Health Checks Table */}\n        <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n          <div className=\"px-4 py-5 sm:px-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900\">Recent Health Checks</h3>\n            <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">\n              Latest health check results for this site\n            </p>\n          </div>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Time\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Response Time\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Jobs\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Visitors\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {healthChecks.slice(0, 10).map((check) => (\n                  <tr key={check.id}>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {format(new Date(check.created_at), 'MMM dd, HH:mm:ss')}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${\n                        check.status === 'up' ? 'bg-green-100 text-green-800' :\n                        check.status === 'down' ? 'bg-red-100 text-red-800' :\n                        'bg-yellow-100 text-yellow-800'\n                      }`}>\n                        {check.status}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {check.response_time ? `${check.response_time.toFixed(0)}ms` : 'N/A'}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      <div className=\"text-xs\">\n                        <div>Done: {check.jobs_done || 0}</div>\n                        <div>Failed: {check.jobs_failed || 0}</div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {check.visitor_count || 0}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;;;AAfA;;;;;;;;;AAiBA,MAAM,SAAS;IAAC;IAAW;IAAW;IAAW;CAAU;AAE5C,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,SAAS,OAAO,EAAE;IAEjC,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACtD,UAAU;YAAC;YAAS;SAAO;QAC3B,OAAO;uCAAE,IAAM,oHAAA,CAAA,WAAQ,CAAC,OAAO,CAAC;;IAClC;IAEA,MAAM,EAAE,MAAM,eAAe,EAAE,EAAE,WAAW,mBAAmB,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC3E,UAAU;YAAC;YAAgB;SAAO;QAClC,OAAO;uCAAE,IAAM,oHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;;IAC7C;IAEA,IAAI,eAAe,qBAAqB;QACtC,qBACE,6LAAC,kJAAA,CAAA,UAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC,kJAAA,CAAA,UAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAAoC;;;;;;;;;;;;;;;;IAI1D;IAEA,qBAAqB;IACrB,MAAM,YAAY,aACf,KAAK,CAAC,CAAC,IAAI,iBAAiB;KAC5B,GAAG,CAAC,CAAA,QAAS,CAAC;YACb,MAAM,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,UAAU,GAAG;YACzC,cAAc,MAAM,aAAa,IAAI;YACrC,QAAQ,MAAM,MAAM;YACpB,UAAU,MAAM,SAAS,IAAI;YAC7B,aAAa,MAAM,YAAY,IAAI;YACnC,YAAY,MAAM,WAAW,IAAI;YACjC,YAAY,MAAM,WAAW,IAAI;YACjC,cAAc,MAAM,aAAa,IAAI;QACvC,CAAC;IAEH,uBAAuB;IACvB,MAAM,eAAe,aAAa,KAAK,CAAC,CAAC;IACzC,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,aAAa,IAAI,CAAC,GAAG,KAAK,aAAa,MAAM;IACtH,MAAM,SAAS,AAAC,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,MAAM,MAAM,GAAG,aAAa,MAAM,GAAI;IAEpG,MAAM,cAAc,YAAY,CAAC,EAAE;IACnC,MAAM,YAAY,cAChB,CAAC,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,IAC7D,CAAC,YAAY,WAAW,IAAI,CAAC,IAAI,CAAC,YAAY,WAAW,IAAI,CAAC,IAAI;IAEpE,MAAM,WAAW,cAAc;QAC7B;YAAE,MAAM;YAAQ,OAAO,YAAY,SAAS,IAAI;YAAG,OAAO;QAAU;QACpE;YAAE,MAAM;YAAW,OAAO,YAAY,YAAY,IAAI;YAAG,OAAO;QAAU;QAC1E;YAAE,MAAM;YAAU,OAAO,YAAY,WAAW,IAAI;YAAG,OAAO;QAAU;QACxE;YAAE,MAAM;YAAU,OAAO,YAAY,WAAW,IAAI;YAAG,OAAO;QAAU;KACzE,GAAG,EAAE;IAEN,qBACE,6LAAC,kJAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC,KAAK,IAAI;;;;;;sDAC3D,6LAAC;4CAAE,WAAU;;gDAAyB,KAAK,UAAU;gDAAC;gDAAE,KAAK,IAAI;;;;;;;;;;;;;;;;;;;sCAGrE,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,MAAM,KAAK,sBAAQ,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAC/C,KAAK,MAAM,KAAK,wBAAU,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCACnD,KAAK,MAAM,KAAK,2BAAa,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CAC/C,6LAAC;oCAAK,WAAW,AAAC,8CAIjB,OAHC,KAAK,MAAM,KAAK,OAAO,gCACvB,KAAK,MAAM,KAAK,SAAS,4BACzB;8CAEC,KAAK,MAAM,CAAC,WAAW;;;;;;;;;;;;;;;;;;8BAM9B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;;4DAAqC,OAAO,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;;4DAAqC,gBAAgB,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAqC,CAAA,wBAAA,kCAAA,YAAa,aAAa,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7F,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wCAAC,MAAM;;0DACf,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0DACN,6LAAC,0JAAA,CAAA,UAAO;gDACN,WAAW,CAAC,OAAO,OAAS;wDAAE,GAAQ,OAAN,OAAM;wDAAK;qDAAgB;gDAC3D,gBAAgB,CAAC,QAAU,AAAC,SAAc,OAAN;;;;;;0DAEtC,6LAAC,uJAAA,CAAA,OAAI;gDACH,MAAK;gDACL,SAAQ;gDACR,QAAO;gDACP,aAAa;gDACb,KAAK;oDAAE,MAAM;oDAAW,aAAa;oDAAG,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;;;;;sCAOrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;0DACP,6LAAC,kJAAA,CAAA,MAAG;gDACF,MAAM;gDACN,IAAG;gDACH,IAAG;gDACH,WAAW;gDACX,OAAO;wDAAC,EAAE,IAAI,EAAE,KAAK,EAAE;2DAAK,AAAC,GAAW,OAAT,MAAK,MAAU,OAAN;;gDACxC,aAAa;gDACb,MAAK;gDACL,SAAQ;0DAEP,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,MAAM,KAAK;uDAAlC,AAAC,QAAa,OAAN;;;;;;;;;;0DAGvB,6LAAC,0JAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wCAAC,MAAM;;0DACf,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0DACN,6LAAC,0JAAA,CAAA,UAAO;;;;;0DACR,6LAAC,uJAAA,CAAA,OAAI;gDAAC,MAAK;gDAAW,SAAQ;gDAAW,SAAQ;gDAAI,QAAO;gDAAU,MAAK;;;;;;0DAC3E,6LAAC,uJAAA,CAAA,OAAI;gDAAC,MAAK;gDAAW,SAAQ;gDAAc,SAAQ;gDAAI,QAAO;gDAAU,MAAK;;;;;;0DAC9E,6LAAC,uJAAA,CAAA,OAAI;gDAAC,MAAK;gDAAW,SAAQ;gDAAa,SAAQ;gDAAI,QAAO;gDAAU,MAAK;;;;;;0DAC7E,6LAAC,uJAAA,CAAA,OAAI;gDAAC,MAAK;gDAAW,SAAQ;gDAAa,SAAQ;gDAAI,QAAO;gDAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;sCAMnF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wCAAC,MAAM;;0DACd,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0DACN,6LAAC,0JAAA,CAAA,UAAO;;;;;0DACR,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAe,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAC5D,6LAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;sCAItD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;;;;;;;;;;;;kDAKnG,6LAAC;wCAAM,WAAU;kDACd,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,sBAC9B,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,UAAU,GAAG;;;;;;kEAEtC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAW,AAAC,iEAIjB,OAHC,MAAM,MAAM,KAAK,OAAO,gCACxB,MAAM,MAAM,KAAK,SAAS,4BAC1B;sEAEC,MAAM,MAAM;;;;;;;;;;;kEAGjB,6LAAC;wDAAG,WAAU;kEACX,MAAM,aAAa,GAAG,AAAC,GAAiC,OAA/B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAG,QAAM;;;;;;kEAEjE,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEAAI;wEAAO,MAAM,SAAS,IAAI;;;;;;;8EAC/B,6LAAC;;wEAAI;wEAAS,MAAM,WAAW,IAAI;;;;;;;;;;;;;;;;;;kEAGvC,6LAAC;wDAAG,WAAU;kEACX,MAAM,aAAa,IAAI;;;;;;;+CAvBnB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCnC;GArTwB;;QACP,qIAAA,CAAA,YAAS;QAGuB,8KAAA,CAAA,WAAQ;QAKa,8KAAA,CAAA,WAAQ;;;KATtD", "debugId": null}}]}