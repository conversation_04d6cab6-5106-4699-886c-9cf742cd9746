import axios from 'axios';

// Create axios instance with base URL and default headers
const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types
export interface Site {
  id: number;
  name: string;
  ip_address: string;
  port: number;
  status: 'up' | 'down' | 'unknown';
  last_check_at: string | null;
  response_time: number | null;
  created_at: string;
  updated_at: string;
  latest_health_check?: HealthCheck;
  health_checks_count?: number;
}

export interface HealthCheck {
  id: number;
  site_id: number;
  status: 'up' | 'down' | 'unknown';
  response_time: number | null;
  jobs_done: number | null;
  jobs_pending: number | null;
  jobs_failed: number | null;
  jobs_queued: number | null;
  last_sync_at: string | null;
  visitor_count: number | null;
  created_at: string;
  updated_at: string;
}

export interface Alert {
  id: number;
  site_id: number;
  alert_type: 'downtime' | 'performance';
  email: string;
  is_active: boolean;
  last_sent_at: string | null;
  created_at: string;
  updated_at: string;
}

// API functions for Sites
export const sitesApi = {
  getAll: async (): Promise<Site[]> => {
    const response = await api.get('/sites');
    return response.data;
  },

  getById: async (id: number): Promise<Site> => {
    const response = await api.get(`/sites/${id}`);
    return response.data;
  },

  create: async (site: Omit<Site, 'id' | 'created_at' | 'updated_at'>): Promise<Site> => {
    const response = await api.post('/sites', { site });
    return response.data;
  },

  update: async (id: number, site: Partial<Site>): Promise<Site> => {
    const response = await api.put(`/sites/${id}`, { site });
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/sites/${id}`);
  },
};

// API functions for Health Checks
export const healthChecksApi = {
  getBySiteId: async (
    siteId: number,
    startDate?: Date | null,
    endDate?: Date | null
  ): Promise<HealthCheck[]> => {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate.toISOString());
    if (endDate) params.append('end_date', endDate.toISOString());

    const url = `/sites/${siteId}/health_checks${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await api.get(url);
    return response.data;
  },

  getById: async (id: number): Promise<HealthCheck> => {
    const response = await api.get(`/health_checks/${id}`);
    return response.data;
  },

  create: async (
    siteId: number,
    healthCheck: Omit<HealthCheck, 'id' | 'site_id' | 'created_at' | 'updated_at'>
  ): Promise<HealthCheck> => {
    const response = await api.post(`/sites/${siteId}/health_checks`, { health_check: healthCheck });
    return response.data;
  },

  getAllWithDateRange: async (
    startDate?: Date | null,
    endDate?: Date | null
  ): Promise<HealthCheck[]> => {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate.toISOString());
    if (endDate) params.append('end_date', endDate.toISOString());

    const url = `/health_checks${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await api.get(url);
    return response.data;
  },
};

// API functions for Alerts
export const alertsApi = {
  getBySiteId: async (siteId: number): Promise<Alert[]> => {
    const response = await api.get(`/sites/${siteId}/alerts`);
    return response.data;
  },

  create: async (
    siteId: number,
    alert: Omit<Alert, 'id' | 'site_id' | 'created_at' | 'updated_at'>
  ): Promise<Alert> => {
    const response = await api.post(`/sites/${siteId}/alerts`, { alert });
    return response.data;
  },

  update: async (id: number, alert: Partial<Alert>): Promise<Alert> => {
    const response = await api.put(`/alerts/${id}`, { alert });
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/alerts/${id}`);
  },
};

export default api;
