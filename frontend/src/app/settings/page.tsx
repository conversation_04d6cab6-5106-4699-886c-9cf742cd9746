'use client';

import DashboardLayout from '@/components/layout/DashboardLayout';
import { Settings } from 'lucide-react';

export default function SettingsPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="mt-1 text-sm text-gray-600">
            Configure your monitoring application settings
          </p>
        </div>

        {/* Settings Sections */}
        <div className="grid grid-cols-1 gap-6">

          {/* Application Info */}
          <div className="bg-white shadow sm:rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center mb-4">
                <Settings className="h-5 w-5 text-gray-500 mr-2" />
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Application Information
                </h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Version</span>
                  <span className="text-sm text-gray-900">1.0.0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Rails Version</span>
                  <span className="text-sm text-gray-900">8.0.2</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Next.js Version</span>
                  <span className="text-sm text-gray-900">15.4.1</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Database</span>
                  <span className="text-sm text-gray-900">SQLite</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Environment</span>
                  <span className="text-sm text-gray-900">Development</span>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          {/* <div className="flex justify-end">
            <button
              type="button"
              className="bg-blue-600 border border-transparent rounded-md shadow-sm py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Save Settings
            </button>
          </div> */}
        </div>
      </div>
    </DashboardLayout>
  );
}
