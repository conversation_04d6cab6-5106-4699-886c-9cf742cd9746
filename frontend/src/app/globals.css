@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* React DatePicker Styles */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container input {
  width: 100%;
}

.react-datepicker {
  font-family: inherit;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.react-datepicker__header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.react-datepicker__current-month {
  color: #374151;
  font-weight: 600;
}

.react-datepicker__day--selected {
  background-color: #3b82f6;
  color: white;
}

.react-datepicker__day--selected:hover {
  background-color: #2563eb;
}

.react-datepicker__day:hover {
  background-color: #e5e7eb;
}

.react-datepicker__day--in-selecting-range {
  background-color: #dbeafe;
  color: #1e40af;
}

.react-datepicker__day--in-range {
  background-color: #dbeafe;
  color: #1e40af;
}

.react-datepicker__day--range-start,
.react-datepicker__day--range-end {
  background-color: #3b82f6;
  color: white;
}
