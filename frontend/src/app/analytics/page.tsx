'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { sitesApi, healthChecksApi } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import DateRangeFilter from '@/components/DateRangeFilter';
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  BarChart, Bar, PieChart, Pie, Cell
} from 'recharts';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import { TrendingUp, TrendingDown, Activity, AlertTriangle } from 'lucide-react';

const COLORS = ['#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

export default function AnalyticsPage() {
  // Default to last 7 days
  const [startDate, setStartDate] = useState<Date | null>(startOfDay(subDays(new Date(), 6)));
  const [endDate, setEndDate] = useState<Date | null>(endOfDay(new Date()));

  const { data: sites = [], isLoading: sitesLoading } = useQuery({
    queryKey: ['sites'],
    queryFn: sitesApi.getAll,
  });

  // Get health checks for all sites with date filtering
  const healthCheckQueries = useQuery({
    queryKey: ['allHealthChecks', startDate, endDate],
    queryFn: () => healthChecksApi.getAllWithDateRange(startDate, endDate),
  });

  const handleDateRangeChange = (newStartDate: Date | null, newEndDate: Date | null) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
  };

  if (sitesLoading || healthCheckQueries.isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  const allHealthChecks = healthCheckQueries.data || [];

  // Calculate analytics data based on date range
  const getDateRange = () => {
    if (!startDate || !endDate) {
      // Default to last 7 days if no filter
      return Array.from({ length: 7 }, (_, i) => {
        const date = subDays(new Date(), 6 - i);
        return startOfDay(date);
      });
    }

    // Generate array of dates between start and end
    const dates = [];
    const current = new Date(startDate);
    const end = new Date(endDate);

    while (current <= end) {
      dates.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    return dates;
  };

  const dateRange = getDateRange();

  const dailyStats = dateRange.map(date => {
    const dayStart = date;
    const dayEnd = new Date(date.getTime() + 24 * 60 * 60 * 1000);
    
    const dayChecks = allHealthChecks.filter(check => {
      const checkDate = new Date(check.created_at);
      return checkDate >= dayStart && checkDate < dayEnd;
    });

    const upChecks = dayChecks.filter(check => check.status === 'up');
    const downChecks = dayChecks.filter(check => check.status === 'down');
    
    return {
      date: format(date, 'MMM dd'),
      totalChecks: dayChecks.length,
      upChecks: upChecks.length,
      downChecks: downChecks.length,
      avgResponseTime: upChecks.length > 0 
        ? upChecks.reduce((sum, check) => sum + (check.response_time || 0), 0) / upChecks.length
        : 0,
      uptime: dayChecks.length > 0 ? (upChecks.length / dayChecks.length) * 100 : 0,
    };
  });

  // Site status distribution
  const statusDistribution = [
    { name: 'Up', value: sites.filter(site => site.status === 'up').length, color: '#10B981' },
    { name: 'Down', value: sites.filter(site => site.status === 'down').length, color: '#EF4444' },
    { name: 'Unknown', value: sites.filter(site => site.status === 'unknown').length, color: '#F59E0B' },
  ].filter(item => item.value > 0);

  // Overall statistics
  const totalChecks = allHealthChecks.length;
  const upChecks = allHealthChecks.filter(check => check.status === 'up').length;
  const overallUptime = totalChecks > 0 ? (upChecks / totalChecks) * 100 : 0;
  const avgResponseTime = upChecks > 0 
    ? allHealthChecks
        .filter(check => check.status === 'up' && check.response_time)
        .reduce((sum, check) => sum + (check.response_time || 0), 0) / upChecks
    : 0;

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
            <p className="mt-1 text-sm text-gray-600">
              Comprehensive analytics and insights for your monitored sites
            </p>
          </div>

          <DateRangeFilter
            startDate={startDate}
            endDate={endDate}
            onDateRangeChange={handleDateRangeChange}
          />
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Activity className="h-6 w-6 text-blue-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Overall Uptime</dt>
                    <dd className="text-lg font-medium text-gray-900">{overallUptime.toFixed(1)}%</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-6 w-6 text-green-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Avg Response Time</dt>
                    <dd className="text-lg font-medium text-gray-900">{avgResponseTime.toFixed(0)}ms</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-6 w-6 text-yellow-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Checks</dt>
                    <dd className="text-lg font-medium text-gray-900">{totalChecks}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingDown className="h-6 w-6 text-red-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Failed Checks</dt>
                    <dd className="text-lg font-medium text-gray-900">{totalChecks - upChecks}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Uptime Trend */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Uptime Trend {startDate && endDate && `(${format(startDate, 'MMM dd')} - ${format(endDate, 'MMM dd')})`}
            </h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={dailyStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis domain={[0, 100]} />
                <Tooltip 
                  formatter={(value, name) => [`${Number(value).toFixed(1)}%`, 'Uptime']}
                />
                <Line 
                  type="monotone" 
                  dataKey="uptime" 
                  stroke="#10B981" 
                  strokeWidth={3}
                  dot={{ fill: '#10B981', strokeWidth: 2, r: 5 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* Site Status Distribution */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Site Status Distribution</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Response Time Trend */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Average Response Time Trend</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={dailyStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [`${Number(value).toFixed(0)}ms`, 'Avg Response Time']}
                />
                <Bar dataKey="avgResponseTime" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* Health Check Volume */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Daily Health Check Volume</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={dailyStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="upChecks" stackId="a" fill="#10B981" name="Successful" />
                <Bar dataKey="downChecks" stackId="a" fill="#EF4444" name="Failed" />
                <Legend />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Summary Table */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Site Performance Summary</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Site
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Health Checks
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Avg Response Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Check
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sites.map((site) => {
                  const siteChecks = allHealthChecks.filter(check => check.site_id === site.id);
                  const siteUpChecks = siteChecks.filter(check => check.status === 'up');
                  const siteAvgResponse = siteUpChecks.length > 0 
                    ? siteUpChecks.reduce((sum, check) => sum + (check.response_time || 0), 0) / siteUpChecks.length
                    : 0;

                  return (
                    <tr key={site.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{site.name}</div>
                        <div className="text-sm text-gray-500">{site.ip_address}:{site.port}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          site.status === 'up' ? 'bg-green-100 text-green-800' :
                          site.status === 'down' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {site.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {siteChecks.length}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {siteAvgResponse.toFixed(0)}ms
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {site.last_check_at 
                          ? format(new Date(site.last_check_at), 'MMM dd, HH:mm')
                          : 'Never'
                        }
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
