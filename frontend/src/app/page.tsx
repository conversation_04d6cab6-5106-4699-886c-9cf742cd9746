'use client';

import { useQuery } from '@tanstack/react-query';
import { sitesApi } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { AlertTriangle, CheckCircle, Clock, Server, ExternalLink } from 'lucide-react';
import Link from 'next/link';

export default function Home() {
  const { data: sites = [], isLoading, error } = useQuery({
    queryKey: ['sites'],
    queryFn: sitesApi.getAll,
  });

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading sites</h3>
              <p className="mt-1 text-sm text-red-700">
                Unable to connect to the monitoring service. Please check if the Rails server is running.
              </p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const upSites = sites.filter(site => site.status === 'up');
  const downSites = sites.filter(site => site.status === 'down');
  const unknownSites = sites.filter(site => site.status === 'unknown');

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-600">
            Monitor the health and performance of your Rails applications
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Server className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Sites</dt>
                    <dd className="text-lg font-medium text-gray-900">{sites.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-6 w-6 text-green-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Sites Up</dt>
                    <dd className="text-lg font-medium text-green-600">{upSites.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-6 w-6 text-red-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Sites Down</dt>
                    <dd className="text-lg font-medium text-red-600">{downSites.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Clock className="h-6 w-6 text-yellow-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Unknown Status</dt>
                    <dd className="text-lg font-medium text-yellow-600">{unknownSites.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sites List */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Site Status</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Overview of all monitored sites and their current status
            </p>
          </div>
          <ul className="divide-y divide-gray-200">
            {sites.map((site) => (
              <li key={site.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        {site.status === 'up' && <CheckCircle className="h-5 w-5 text-green-400" />}
                        {site.status === 'down' && <AlertTriangle className="h-5 w-5 text-red-400" />}
                        {site.status === 'unknown' && <Clock className="h-5 w-5 text-yellow-400" />}
                      </div>
                      <div className="ml-4">
                        <Link href={`/sites/${site.id}`} className="group flex items-center">
                          <div className="text-sm font-medium text-gray-900 group-hover:text-blue-600">{site.name}</div>
                          <ExternalLink className="h-3 w-3 ml-1 text-gray-400 group-hover:text-blue-600" />
                        </Link>
                        <div className="text-sm text-gray-500">{site.ip_address}:{site.port}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      {site.response_time && (
                        <div className="text-sm text-gray-500">
                          {site.response_time.toFixed(0)}ms
                        </div>
                      )}
                      <div className="text-sm text-gray-500">
                        {site.last_check_at
                          ? new Date(site.last_check_at).toLocaleString()
                          : 'Never checked'
                        }
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
          {sites.length === 0 && (
            <div className="text-center py-12">
              <Server className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No sites</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by adding your first site to monitor.</p>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
