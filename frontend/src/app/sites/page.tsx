'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { sitesApi } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Plus, Edit, Trash2, <PERSON><PERSON><PERSON>riangle, <PERSON>Circle, Clock, ExternalLink } from 'lucide-react';
import Link from 'next/link';

export default function SitesPage() {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingSite, setEditingSite] = useState<any>(null);
  const queryClient = useQueryClient();

  const { data: sites = [], isLoading, error } = useQuery({
    queryKey: ['sites'],
    queryFn: sitesApi.getAll,
  });

  const createMutation = useMutation({
    mutationFn: sitesApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sites'] });
      setShowAddForm(false);
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, ...data }: any) => sitesApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sites'] });
      setEditingSite(null);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: sitesApi.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sites'] });
    },
  });

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const data = {
      name: formData.get('name') as string,
      ip_address: formData.get('ip_address') as string,
      port: parseInt(formData.get('port') as string),
    };

    if (editingSite) {
      updateMutation.mutate({ id: editingSite.id, ...data });
    } else {
      createMutation.mutate(data);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading sites</h3>
              <p className="mt-1 text-sm text-red-700">
                Unable to connect to the monitoring service.
              </p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Sites</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage the sites you want to monitor
            </p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Site
          </button>
        </div>

        {/* Add/Edit Form */}
        {(showAddForm || editingSite) && (
          <div className="bg-white shadow sm:rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                {editingSite ? 'Edit Site' : 'Add New Site'}
              </h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Site Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    id="name"
                    required
                    defaultValue={editingSite?.name || ''}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black p-2"
                    placeholder="My Application"
                  />
                </div>
                <div>
                  <label htmlFor="ip_address" className="block text-sm font-medium text-gray-700">
                    IP Address
                  </label>
                  <input
                    type="text"
                    name="ip_address"
                    id="ip_address"
                    required
                    defaultValue={editingSite?.ip_address || ''}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black p-2"
                    placeholder="*************"
                  />
                </div>
                <div>
                  <label htmlFor="port" className="block text-sm font-medium text-gray-700">
                    Port
                  </label>
                  <input
                    type="number"
                    name="port"
                    id="port"
                    required
                    min="1"
                    max="65535"
                    defaultValue={editingSite?.port || ''}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black p-2"
                    placeholder="3000"
                  />
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddForm(false);
                      setEditingSite(null);
                    }}
                    className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={createMutation.isPending || updateMutation.isPending}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                  >
                    {createMutation.isPending || updateMutation.isPending ? 'Saving...' : 'Save'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Sites Table */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Monitored Sites</h3>
          </div>
          <ul className="divide-y divide-gray-200">
            {sites.map((site) => (
              <li key={site.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        {site.status === 'up' && <CheckCircle className="h-5 w-5 text-green-400" />}
                        {site.status === 'down' && <AlertTriangle className="h-5 w-5 text-red-400" />}
                        {site.status === 'unknown' && <Clock className="h-5 w-5 text-yellow-400" />}
                      </div>
                      <div className="ml-4">
                        <Link href={`/sites/${site.id}`} className="group flex items-center">
                          <div className="text-sm font-medium text-gray-900 group-hover:text-blue-600">{site.name}</div>
                          <ExternalLink className="h-3 w-3 ml-1 text-gray-400 group-hover:text-blue-600" />
                        </Link>
                        <div className="text-sm text-gray-500">{site.ip_address}:{site.port}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setEditingSite(site)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          if (confirm('Are you sure you want to delete this site?')) {
                            deleteMutation.mutate(site.id);
                          }
                        }}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
          {sites.length === 0 && (
            <div className="text-center py-12">
              <h3 className="mt-2 text-sm font-medium text-gray-900">No sites</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by adding your first site to monitor.</p>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
