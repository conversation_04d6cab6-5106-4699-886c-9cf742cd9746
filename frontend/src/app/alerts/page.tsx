'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { sitesApi, alertsApi, Site, Alert } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Bell, Plus, Edit, Trash2, AlertTriangle } from 'lucide-react';

export default function AlertsPage() {
  const [selectedSite, setSelectedSite] = useState<Site | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAlert, setEditingAlert] = useState<Alert | null>(null);
  const queryClient = useQueryClient();

  const { data: sites = [], isLoading: sitesLoading } = useQuery({
    queryKey: ['sites'],
    queryFn: sitesApi.getAll,
  });

  const { data: alerts = [], isLoading: alertsLoading } = useQuery({
    queryKey: ['alerts', selectedSite?.id],
    queryFn: () => selectedSite ? alertsApi.getBySiteId(selectedSite.id) : Promise.resolve([]),
    enabled: !!selectedSite,
  });

  const createMutation = useMutation({
    mutationFn: (data: { siteId: number; alert: any }) => 
      alertsApi.create(data.siteId, data.alert),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alerts', selectedSite?.id] });
      setShowAddForm(false);
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, ...data }: any) => alertsApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alerts', selectedSite?.id] });
      setEditingAlert(null);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: alertsApi.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alerts', selectedSite?.id] });
    },
  });

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const data = {
      alert_type: formData.get('alert_type') as string,
      email: formData.get('email') as string,
      is_active: formData.get('is_active') === 'on',
    };

    if (editingAlert) {
      updateMutation.mutate({ id: editingAlert.id, ...data });
    } else if (selectedSite) {
      createMutation.mutate({ siteId: selectedSite.id, alert: data });
    }
  };

  const isLoading = sitesLoading || alertsLoading;

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Alerts</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage email alerts for your monitored sites
            </p>
          </div>
        </div>

        {/* Site Selection */}
        <div className="bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Select a Site
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {sites.map((site) => (
                <div
                  key={site.id}
                  className={`border rounded-lg p-4 cursor-pointer ${
                    selectedSite?.id === site.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-blue-300'
                  }`}
                  onClick={() => setSelectedSite(site)}
                >
                  <div className="flex items-center">
                    <Bell className="h-5 w-5 text-blue-500 mr-2" />
                    <div>
                      <div className="font-medium">{site.name}</div>
                      <div className="text-sm text-gray-500">
                        {site.ip_address}:{site.port}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Alerts Management */}
        {selectedSite && (
          <div className="bg-white shadow sm:rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Alerts for {selectedSite.name}
                </h3>
                <button
                  onClick={() => setShowAddForm(true)}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Alert
                </button>
              </div>

              {/* Add/Edit Form */}
              {(showAddForm || editingAlert) && (
                <div className="bg-gray-50 p-4 rounded-md mb-4">
                  <h4 className="text-md font-medium text-gray-900 mb-3">
                    {editingAlert ? 'Edit Alert' : 'Add New Alert'}
                  </h4>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <label htmlFor="alert_type" className="block text-sm font-medium text-gray-700">
                        Alert Type
                      </label>
                      <select
                        id="alert_type"
                        name="alert_type"
                        defaultValue={editingAlert?.alert_type || 'downtime'}
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      >
                        <option value="downtime">Downtime Alert</option>
                        <option value="performance">Performance Alert</option>
                      </select>
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Email Address
                      </label>
                      <input
                        type="email"
                        name="email"
                        id="email"
                        required
                        defaultValue={editingAlert?.email || ''}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="flex items-center">
                      <input
                        id="is_active"
                        name="is_active"
                        type="checkbox"
                        defaultChecked={editingAlert ? editingAlert.is_active : true}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                        Active
                      </label>
                    </div>
                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={() => {
                          setShowAddForm(false);
                          setEditingAlert(null);
                        }}
                        className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={createMutation.isPending || updateMutation.isPending}
                        className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                      >
                        {createMutation.isPending || updateMutation.isPending ? 'Saving...' : 'Save'}
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {/* Alerts List */}
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : alerts.length > 0 ? (
                <ul className="divide-y divide-gray-200">
                  {alerts.map((alert) => (
                    <li key={alert.id} className="py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className={`p-2 rounded-full ${
                            alert.alert_type === 'downtime' ? 'bg-red-100' : 'bg-yellow-100'
                          }`}>
                            <AlertTriangle className={`h-5 w-5 ${
                              alert.alert_type === 'downtime' ? 'text-red-500' : 'text-yellow-500'
                            }`} />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">
                              {alert.alert_type === 'downtime' ? 'Downtime Alert' : 'Performance Alert'}
                            </p>
                            <p className="text-sm text-gray-500">{alert.email}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            alert.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {alert.is_active ? 'Active' : 'Inactive'}
                          </span>
                          <button
                            onClick={() => setEditingAlert(alert)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => {
                              if (confirm('Are you sure you want to delete this alert?')) {
                                deleteMutation.mutate(alert.id);
                              }
                            }}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-center py-8">
                  <Bell className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No alerts</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Get started by adding an alert for this site.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
