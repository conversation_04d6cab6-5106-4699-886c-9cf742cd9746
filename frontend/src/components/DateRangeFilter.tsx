'use client';

import { useState } from 'react';
import DatePicker from 'react-datepicker';
import { Calendar, X } from 'lucide-react';
import { subDays, startOfDay, endOfDay } from 'date-fns';
import 'react-datepicker/dist/react-datepicker.css';

interface DateRangeFilterProps {
  startDate: Date | null;
  endDate: Date | null;
  onDateRangeChange: (startDate: Date | null, endDate: Date | null) => void;
  className?: string;
}

export default function DateRangeFilter({
  startDate,
  endDate,
  onDateRangeChange,
  className = ''
}: DateRangeFilterProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleStartDateChange = (date: Date | null) => {
    const adjustedDate = date ? startOfDay(date) : null;
    onDateRangeChange(adjustedDate, endDate);
  };

  const handleEndDateChange = (date: Date | null) => {
    const adjustedDate = date ? endOfDay(date) : null;
    onDateRangeChange(startDate, adjustedDate);
  };

  const clearDateRange = () => {
    onDateRangeChange(null, null);
  };

  const setPresetRange = (days: number) => {
    const end = endOfDay(new Date());
    const start = startOfDay(subDays(end, days - 1));
    onDateRangeChange(start, end);
  };

  const formatDateRange = () => {
    if (!startDate && !endDate) return 'Select date range';
    if (startDate && !endDate) return `From ${startDate.toLocaleDateString()}`;
    if (!startDate && endDate) return `Until ${endDate.toLocaleDateString()}`;
    return `${startDate?.toLocaleDateString()} - ${endDate?.toLocaleDateString()}`;
  };

  return (
    <div className={`relative ${className}`}>
      <div className="flex items-center space-x-2">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Calendar className="h-4 w-4 mr-2" />
          {formatDateRange()}
        </button>
        
        {(startDate || endDate) && (
          <button
            onClick={clearDateRange}
            className="inline-flex items-center p-1 border border-gray-300 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-50"
            title="Clear date filter"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4 min-w-max">
          <div className="space-y-4">
            {/* Preset Buttons */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setPresetRange(1)}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md text-black"
              >
                Today
              </button>
              <button
                onClick={() => setPresetRange(7)}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md text-black"
              >
                Last 7 days
              </button>
              <button
                onClick={() => setPresetRange(30)}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md text-black"
              >
                Last 30 days
              </button>
              <button
                onClick={() => setPresetRange(90)}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md  text-black"
              >
                Last 90 days
              </button>
            </div>

            {/* Date Pickers */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1 text-black">
                  Start Date
                </label>
                <DatePicker
                  selected={startDate}
                  onChange={handleStartDateChange}
                  selectsStart
                  startDate={startDate}
                  endDate={endDate}
                  maxDate={new Date()}
                  className="text-black w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholderText="Select start date"
                  dateFormat="MMM dd, yyyy"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1  text-black">
                  End Date
                </label>
                <DatePicker
                  selected={endDate}
                  onChange={handleEndDateChange}
                  selectsEnd
                  startDate={startDate}
                  endDate={endDate}
                  minDate={startDate}
                  maxDate={new Date()}
                  className="text-black w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholderText="Select end date"
                  dateFormat="MMM dd, yyyy"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 pt-2 border-t">
              <button
                onClick={() => setIsOpen(false)}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800  text-black"
              >
                Close
              </button>
              <button
                onClick={() => {
                  clearDateRange();
                  setIsOpen(false);
                }}
                className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md text-black"
              >
                Clear
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
