'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { sitesApi, FailedJob } from '@/lib/api';
import { X, AlertTriangle, Clock, RefreshCw, Code } from 'lucide-react';
import { format } from 'date-fns';

interface FailedJobsModalProps {
  siteId: number;
  siteName: string;
  isOpen: boolean;
  onClose: () => void;
}

export default function FailedJobsModal({ siteId, siteName, isOpen, onClose }: FailedJobsModalProps) {
  const [expandedJob, setExpandedJob] = useState<string | number | null>(null);

  const { data: failedJobsData, isLoading, error, refetch } = useQuery({
    queryKey: ['failedJobs', siteId],
    queryFn: () => sitesApi.getFailedJobs(siteId),
    enabled: isOpen,
  });

  if (!isOpen) return null;

  const handleRefresh = () => {
    refetch();
  };

  const toggleJobExpansion = (jobId: string | number) => {
    setExpandedJob(expandedJob === jobId ? null : jobId);
  };

  const formatArguments = (args: unknown) => {
    if (!args || typeof args !== 'object') return 'None';
    try {
      return JSON.stringify(args, null, 2);
    } catch {
      return String(args);
    }
  };

  const truncateError = (error: string, maxLength: number = 200) => {
    if (error.length <= maxLength) return error;
    return error.substring(0, maxLength) + '...';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-6 w-6 text-red-500" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Failed Jobs</h2>
              <p className="text-sm text-gray-600">{siteName}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 text-black"
              title="Refresh failed jobs"
            >
              <RefreshCw className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={onClose}
              className="p-2 rounded-md hover:bg-gray-100  text-black"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <AlertTriangle className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error loading failed jobs</h3>
                  <p className="mt-1 text-sm text-red-700">
                    {failedJobsData?.error || 'Unable to fetch failed jobs from the monitored site.'}
                  </p>
                </div>
              </div>
            </div>
          ) : !failedJobsData?.failed_jobs?.length ? (
            <div className="text-center py-12">
              <AlertTriangle className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No failed jobs</h3>
              <p className="mt-1 text-sm text-gray-500">
                Great! There are currently no failed jobs for this site.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  Showing {failedJobsData.failed_jobs.length} failed job{failedJobsData.failed_jobs.length !== 1 ? 's' : ''}
                </p>
              </div>

              {failedJobsData.failed_jobs.map((job: FailedJob) => (
                <div key={job.id} className="bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Code className="h-4 w-4 text-gray-500" />
                          <h3 className="text-sm font-medium text-gray-900">{job.job_class}</h3>
                          <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                            {job.queue_name}
                          </span>
                          {job.priority > 0 && (
                            <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                              Priority: {job.priority}
                            </span>
                          )}
                        </div>
                        
                        <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>Failed: {format(new Date(job.failed_at), 'MMM dd, yyyy HH:mm:ss')}</span>
                          </div>
                          {job.retry_count > 0 && (
                            <span>Retries: {job.retry_count}</span>
                          )}
                          {job.active_job_id && (
                            <span>Job ID: {job.active_job_id}</span>
                          )}
                        </div>

                        <div className="mt-3">
                          <p className="text-sm text-red-600 font-medium">Error:</p>
                          <p className="text-sm text-gray-700 mt-1 font-mono bg-red-50 p-2 rounded border">
                            {expandedJob === job.id ? job.error_message : truncateError(job.error_message)}
                          </p>
                        </div>
                      </div>

                      <button
                        onClick={() => toggleJobExpansion(job.id)}
                        className="ml-4 px-3 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded-md text-black"
                      >
                        {expandedJob === job.id ? 'Less' : 'More'}
                      </button>
                    </div>

                    {expandedJob === job.id && (
                      <div className="mt-4 pt-4 border-t border-gray-200 space-y-3">
                        {job.arguments && Object.keys(job.arguments).length > 0 && (
                          <div>
                            <p className="text-sm font-medium text-gray-700">Arguments:</p>
                            <pre className="text-xs text-gray-600 mt-1 bg-gray-100 p-2 rounded border overflow-x-auto">
                              {formatArguments(job.arguments)}
                            </pre>
                          </div>
                        )}

                        <div className="grid grid-cols-2 gap-4 text-xs">
                          {job.scheduled_at && (
                            <div>
                              <p className="font-medium text-gray-700">Scheduled At:</p>
                              <p className="text-gray-600">{format(new Date(job.scheduled_at), 'MMM dd, yyyy HH:mm:ss')}</p>
                            </div>
                          )}
                          {job.finished_at && (
                            <div>
                              <p className="font-medium text-gray-700">Finished At:</p>
                              <p className="text-gray-600">{format(new Date(job.finished_at), 'MMM dd, yyyy HH:mm:ss')}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t p-4 bg-gray-50">
          <div className="flex justify-between items-center">
            <p className="text-xs text-gray-500">
              Data fetched from {siteName} • Last updated: {new Date().toLocaleTimeString()}
            </p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
