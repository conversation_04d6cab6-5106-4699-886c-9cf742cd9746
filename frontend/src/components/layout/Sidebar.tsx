'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { BarChart3, Bell, Home, Server, Settings } from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Sites', href: '/sites', icon: Server },
  { name: '<PERSON><PERSON><PERSON>', href: '/alerts', icon: Bell },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  { name: 'Settings', href: '/settings', icon: Settings },
];

export default function Sidebar() {
  const pathname = usePathname();

  return (
    <div className="flex h-full flex-col bg-gray-900 text-white w-64">
      <div className="flex h-16 shrink-0 items-center px-6 border-b border-gray-800">
        <h1 className="text-xl font-bold">CDR Site Monitor</h1>
      </div>
      <nav className="flex flex-1 flex-col py-4">
        <ul className="flex flex-1 flex-col gap-y-4 px-4">
          {navigation.map((item) => (
            <li key={item.name}>
              <Link
                href={item.href}
                className={`flex items-center gap-x-3 rounded-md px-3 py-2 text-sm font-medium ${
                  pathname === item.href
                    ? 'bg-gray-800 text-white'
                    : 'text-gray-400 hover:bg-gray-800 hover:text-white'
                }`}
              >
                <item.icon className="h-5 w-5" aria-hidden="true" />
                {item.name}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      <div className="border-t border-gray-800 p-4">
        <div className="flex items-center gap-x-3">
          <div className="h-8 w-8 rounded-full bg-gray-800"></div>
          {/* <div>
            <p className="text-sm font-medium text-white">Admin User</p>
            <p className="text-xs text-gray-400"><EMAIL></p>
          </div> */}
        </div>
      </div>
    </div>
  );
}
