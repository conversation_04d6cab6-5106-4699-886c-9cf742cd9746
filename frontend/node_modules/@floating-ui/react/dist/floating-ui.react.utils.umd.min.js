!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).FloatingUIReactUtils={})}(this,(function(t){"use strict";function e(){return"undefined"!=typeof window}function n(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function o(){const t=navigator.userAgentData;return null!=t&&t.platform?t.platform:navigator.platform}function i(){const t=navigator.userAgentData;return t&&Array.isArray(t.brands)?t.brands.map((t=>{let{brand:e,version:n}=t;return e+"/"+n})).join(" "):navigator.userAgent}function r(){const t=/android/i;return t.test(o())||t.test(i())}function u(){return i().includes("jsdom/")}const a="input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])";function s(t){return o=t,!!e()&&(o instanceof HTMLElement||o instanceof n(o).HTMLElement)&&t.matches(a);var o}t.TYPEABLE_SELECTOR=a,t.activeElement=function(t){let e=t.activeElement;for(;null!=(null==(n=e)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;e=e.shadowRoot.activeElement}return e},t.contains=function(t,o){if(!t||!o)return!1;const i=null==o.getRootNode?void 0:o.getRootNode();if(t.contains(o))return!0;if(i&&(r=i,e()&&"undefined"!=typeof ShadowRoot&&(r instanceof ShadowRoot||r instanceof n(r).ShadowRoot))){let e=o;for(;e;){if(t===e)return!0;e=e.parentNode||e.host}}var r;return!1},t.getDocument=function(t){return(null==t?void 0:t.ownerDocument)||document},t.getPlatform=o,t.getTarget=function(t){return"composedPath"in t?t.composedPath()[0]:t.target},t.getUserAgent=i,t.isAndroid=r,t.isEventTargetWithin=function(t,e){if(null==e)return!1;if("composedPath"in t)return t.composedPath().includes(e);const n=t;return null!=n.target&&e.contains(n.target)},t.isJSDOM=u,t.isMac=function(){return o().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints},t.isMouseLikePointerType=function(t,e){const n=["mouse","pen"];return e||n.push("",void 0),n.includes(t)},t.isReactEvent=function(t){return"nativeEvent"in t},t.isRootElement=function(t){return t.matches("html,body")},t.isSafari=function(){return/apple/i.test(navigator.vendor)},t.isTypeableCombobox=function(t){return!!t&&("combobox"===t.getAttribute("role")&&s(t))},t.isTypeableElement=s,t.isVirtualClick=function(t){return!(0!==t.mozInputSource||!t.isTrusted)||(r()&&t.pointerType?"click"===t.type&&1===t.buttons:0===t.detail&&!t.pointerType)},t.isVirtualPointerEvent=function(t){return!u()&&(!r()&&0===t.width&&0===t.height||r()&&1===t.width&&1===t.height&&0===t.pressure&&0===t.detail&&"mouse"===t.pointerType||t.width<1&&t.height<1&&0===t.pressure&&0===t.detail&&"touch"===t.pointerType)},t.stopEvent=function(t){t.preventDefault(),t.stopPropagation()}}));
