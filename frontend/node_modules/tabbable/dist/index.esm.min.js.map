{"version": 3, "file": "index.esm.min.js", "sources": ["../src/index.js"], "sourcesContent": ["// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nconst hasTabIndex = function (node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nconst getTabIndex = function (node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if (\n      (/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      !hasTabIndex(node)\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nconst getSortOrderTabIndex = function (node, isScope) {\n  const tabIndex = getTabIndex(node);\n\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n\n  return tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabIndex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getSortOrderTabIndex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorTabbable.bind(null, options),\n        flatten: false,\n        getShadowRoot: options.getShadowRoot,\n        shadowRootFilter: isValidShadowRootTabbable,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorFocusable.bind(null, options),\n        flatten: true,\n        getShadowRoot: options.getShadowRoot,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable, getTabIndex };\n"], "names": ["candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "isInert", "node", "lookUp", "_node$getAttribute", "inertAtt", "getAttribute", "parentNode", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "nestedCandidates", "children", "flatten", "push", "scopeParent", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasTabIndex", "isNaN", "parseInt", "getTabIndex", "Error", "tabIndex", "test", "_node$getAttribute2", "attValue", "isContentEditable", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isNonTabbableRadio", "type", "isRadio", "name", "radioSet", "radioScope", "form", "queryRadios", "window", "CSS", "escape", "err", "console", "error", "message", "checked", "nodes", "i", "getCheckedRadio", "isTabbableRadio", "isZeroArea", "_node$getBoundingClie", "getBoundingClientRect", "width", "height", "isHidden", "_ref", "displayCheck", "getComputedStyle", "visibility", "nodeUnderDetails", "parentElement", "originalNode", "rootNode", "assignedSlot", "host", "_nodeRoot", "_nodeRootHost", "_nodeRootHost$ownerDo", "_node$ownerDocument", "nodeRoot", "nodeRootHost", "attached", "contains", "_nodeRoot2", "_nodeRootHost2", "_nodeRootHost2$ownerD", "isNodeAttached", "getClientRects", "isNodeMatchingSelectorFocusable", "disabled", "isHiddenInput", "some", "child", "isDetailsWithSummary", "item", "isDisabledFromFieldset", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "isScope", "candidateTabindex", "getSortOrderTabIndex", "content", "sort", "reduce", "acc", "sortable", "concat", "tabbable", "container", "bind", "focusable", "isTabbable", "focusableCandidateSelector", "isFocusable"], "mappings": ";;;;AAKA,IAAMA,EAAqB,CACzB,qBACA,sBACA,wBACA,uBACA,sBACA,oCACA,+BACA,+BACA,gEACA,6CACA,wBAEIC,EAAoCD,EAAmBE,KAAK,KAE5DC,EAA+B,oBAAZC,QAEnBC,EAAUF,EACZ,aACAC,QAAQE,UAAUD,SAClBD,QAAQE,UAAUC,mBAClBH,QAAQE,UAAUE,sBAEhBC,GACHN,GAAaC,QAAQE,UAAUG,YAC5B,SAACC,GAAO,IAAAC,EAAA,OAAKD,SAAoB,QAAbC,EAAPD,EAASD,mBAATE,IAAoBA,OAAbA,EAAPA,EAAAC,KAAAF,EAAwB,EACrC,SAACA,GAAO,OAAKA,aAAAA,EAAAA,EAASG,aAAa,EAUnCC,EAAU,SAAVA,EAAoBC,EAAMC,GAAe,IAAAC,OAAT,IAAND,IAAAA,GAAS,GAIvC,IAAME,EAAWH,SAAkBE,QAAdA,EAAJF,EAAMI,wBAAYF,OAAdA,EAAJA,EAAAL,KAAAG,EAAqB,SAUtC,MAT2B,KAAbG,GAAgC,SAAbA,GAORF,GAAUD,GAAQD,EAAQC,EAAKK,WAG1D,EAqBMC,EAAgB,SAAUC,EAAIC,EAAkBC,GAGpD,GAAIV,EAAQQ,GACV,MAAO,GAGT,IAAIG,EAAaC,MAAMpB,UAAUqB,MAAMC,MACrCN,EAAGO,iBAAiB5B,IAMtB,OAJIsB,GAAoBlB,EAAQO,KAAKU,EAAIrB,IACvCwB,EAAWK,QAAQR,GAErBG,EAAaA,EAAWD,OAAOA,EAEjC,EAoCMO,EAA2B,SAA3BA,EACJC,EACAT,EACAU,GAIA,IAFA,IAAMR,EAAa,GACbS,EAAkBR,MAAMS,KAAKH,GAC5BE,EAAgBE,QAAQ,CAC7B,IAAM1B,EAAUwB,EAAgBG,QAChC,IAAIvB,EAAQJ,GAAS,GAMrB,GAAwB,SAApBA,EAAQ4B,QAAoB,CAE9B,IAAMC,EAAW7B,EAAQ8B,mBAEnBC,EAAmBV,EADTQ,EAASH,OAASG,EAAW7B,EAAQgC,UACM,EAAMT,GAC7DA,EAAQU,QACVlB,EAAWmB,KAAIhB,MAAfH,EAAmBgB,GAEnBhB,EAAWmB,KAAK,CACdC,YAAanC,EACbe,WAAYgB,GAGlB,KAAO,CAEkBpC,EAAQO,KAAKF,EAAST,IAG3CgC,EAAQT,OAAOd,KACda,IAAqBS,EAASc,SAASpC,KAExCe,EAAWmB,KAAKlC,GAIlB,IAAMqC,EACJrC,EAAQqC,YAE0B,mBAA1Bd,EAAQe,eACdf,EAAQe,cAActC,GAKpBuC,GACHnC,EAAQiC,GAAY,MACnBd,EAAQiB,kBAAoBjB,EAAQiB,iBAAiBxC,IAEzD,GAAIqC,GAAcE,EAAiB,CAOjC,IAAMR,EAAmBV,GACR,IAAfgB,EAAsBrC,EAAQgC,SAAWK,EAAWL,UACpD,EACAT,GAGEA,EAAQU,QACVlB,EAAWmB,KAAIhB,MAAfH,EAAmBgB,GAEnBhB,EAAWmB,KAAK,CACdC,YAAanC,EACbe,WAAYgB,GAGlB,MAGEP,EAAgBJ,QAAOF,MAAvBM,EAA2BxB,EAAQgC,SAEvC,CACF,CACA,OAAOjB,CACT,EAQM0B,EAAc,SAAUpC,GAC5B,OAAQqC,MAAMC,SAAStC,EAAKI,aAAa,YAAa,IACxD,EAQMmC,EAAc,SAAUvC,GAC5B,IAAKA,EACH,MAAM,IAAIwC,MAAM,oBAGlB,OAAIxC,EAAKyC,SAAW,IASf,0BAA0BC,KAAK1C,EAAKuB,UAnLjB,SAAUvB,GAAM,IAAA2C,EAIlCC,EAAW5C,SAAkB2C,QAAdA,EAAJ3C,EAAMI,wBAAYuC,OAAdA,EAAJA,EAAA9C,KAAAG,EAAqB,mBACtC,MAAoB,KAAb4C,GAAgC,SAAbA,CAC5B,CA8KQC,CAAkB7C,MACnBoC,EAAYpC,GAEN,EAIJA,EAAKyC,QACd,EAoBMK,EAAuB,SAAUC,EAAGC,GACxC,OAAOD,EAAEN,WAAaO,EAAEP,SACpBM,EAAEE,cAAgBD,EAAEC,cACpBF,EAAEN,SAAWO,EAAEP,QACrB,EAEMS,EAAU,SAAUlD,GACxB,MAAwB,UAAjBA,EAAKuB,OACd,EA8DM4B,EAAqB,SAAUnD,GACnC,OALc,SAAUA,GACxB,OAAOkD,EAAQlD,IAAuB,UAAdA,EAAKoD,IAC/B,CAGSC,CAAQrD,KAxCO,SAAUA,GAChC,IAAKA,EAAKsD,KACR,OAAO,EAET,IAOIC,EAPEC,EAAaxD,EAAKyD,MAAQ/D,EAAYM,GACtC0D,EAAc,SAAUJ,GAC5B,OAAOE,EAAW1C,iBAChB,6BAA+BwC,EAAO,OAK1C,GACoB,oBAAXK,aACe,IAAfA,OAAOC,KACe,mBAAtBD,OAAOC,IAAIC,OAElBN,EAAWG,EAAYC,OAAOC,IAAIC,OAAO7D,EAAKsD,YAE9C,IACEC,EAAWG,EAAY1D,EAAKsD,KAC7B,CAAC,MAAOQ,GAMP,OAJAC,QAAQC,MACN,2IACAF,EAAIG,UAEC,CACT,CAGF,IAAMC,EAvCgB,SAAUC,EAAOV,GACvC,IAAK,IAAIW,EAAI,EAAGA,EAAID,EAAM9C,OAAQ+C,IAChC,GAAID,EAAMC,GAAGF,SAAWC,EAAMC,GAAGX,OAASA,EACxC,OAAOU,EAAMC,EAGnB,CAiCkBC,CAAgBd,EAAUvD,EAAKyD,MAC/C,OAAQS,GAAWA,IAAYlE,CACjC,CAO2BsE,CAAgBtE,EAC3C,EAoDMuE,EAAa,SAAUvE,GAC3B,IAAAwE,EAA0BxE,EAAKyE,wBAAvBC,EAAKF,EAALE,MAAOC,EAAMH,EAANG,OACf,OAAiB,IAAVD,GAA0B,IAAXC,CACxB,EACMC,EAAW,SAAU5E,EAAI6E,GAAmC,IAA/BC,EAAYD,EAAZC,aAAc7C,EAAa4C,EAAb5C,cAM/C,GAA0C,WAAtC8C,iBAAiB/E,GAAMgF,WACzB,OAAO,EAGT,IACMC,EADkB3F,EAAQO,KAAKG,EAAM,iCACAA,EAAKkF,cAAgBlF,EAChE,GAAIV,EAAQO,KAAKoF,EAAkB,yBACjC,OAAO,EAGT,GACGH,GACgB,SAAjBA,GACiB,gBAAjBA,GAqEK,GAAqB,kBAAjBA,EAMT,OAAOP,EAAWvE,OA1ElB,CACA,GAA6B,mBAAlBiC,EAA8B,CAIvC,IADA,IAAMkD,EAAenF,EACdA,GAAM,CACX,IAAMkF,EAAgBlF,EAAKkF,cACrBE,EAAW1F,EAAYM,GAC7B,GACEkF,IACCA,EAAclD,aACkB,IAAjCC,EAAciD,GAId,OAAOX,EAAWvE,GAGlBA,EAFSA,EAAKqF,aAEPrF,EAAKqF,aACFH,GAAiBE,IAAapF,EAAKF,cAKtCoF,EAHAE,EAASE,IAKpB,CAEAtF,EAAOmF,CACT,CAWA,GAjHmB,SAAUnF,GAAM,IAAAuF,EA8BFC,EAAAC,EAAAC,EAN/BC,EAAW3F,GAAQN,EAAYM,GAC/B4F,UAAYL,EAAGI,SAAQ,IAAAJ,OAAA,EAARA,EAAUD,KAIzBO,GAAW,EACf,GAAIF,GAAYA,IAAa3F,EAM3B,IALA6F,KACcL,QAAZA,EAAAI,aAAYJ,WAAAC,EAAZD,EAAc1F,qBAAa,IAAA2F,GAA3BA,EAA6BK,SAASF,IACtC5F,SAAmB0F,QAAfA,EAAJ1F,EAAMF,yBAAa4F,GAAnBA,EAAqBI,SAAS9F,KAGxB6F,GAAYD,GAAc,CAAA,IAAAG,EAAAC,EAAAC,EAMhCJ,IAAyB,QAAbG,EADZJ,UAAYG,EADZJ,EAAWjG,EAAYkG,UACA,IAAAG,OAAA,EAARA,EAAUT,YACA,IAAAU,WAAAC,EAAZD,EAAclG,qBAAa,IAAAmG,IAA3BA,EAA6BH,SAASF,GACrD,CAGF,OAAOC,CACT,CAkEQK,CAAelG,GAKjB,OAAQA,EAAKmG,iBAAiB9E,OAmBhC,GAAqB,gBAAjByD,EACF,OAAO,CAGX,CAWA,OAAO,CACT,EAmCMsB,EAAkC,SAAUlF,EAASlB,GACzD,QACEA,EAAKqG,UAILtG,EAAQC,IAnQU,SAAUA,GAC9B,OAAOkD,EAAQlD,IAAuB,WAAdA,EAAKoD,IAC/B,CAkQIkD,CAActG,IACd4E,EAAS5E,EAAMkB,IAjQU,SAAUlB,GAMrC,MAJmB,YAAjBA,EAAKuB,SACLZ,MAAMpB,UAAUqB,MACbC,MAAMb,EAAK2B,UACX4E,MAAK,SAACC,GAAK,MAAuB,YAAlBA,EAAMjF,UAE7B,CA4PIkF,CAAqBzG,IAxCM,SAAUA,GACvC,GAAI,mCAAmC0C,KAAK1C,EAAKuB,SAG/C,IAFA,IAAIlB,EAAaL,EAAKkF,cAEf7E,GAAY,CACjB,GAA2B,aAAvBA,EAAWkB,SAA0BlB,EAAWgG,SAAU,CAE5D,IAAK,IAAIjC,EAAI,EAAGA,EAAI/D,EAAWsB,SAASN,OAAQ+C,IAAK,CACnD,IAAMoC,EAAQnG,EAAWsB,SAAS+E,KAAKtC,GAEvC,GAAsB,WAAlBoC,EAAMjF,QAGR,QAAOjC,EAAQO,KAAKQ,EAAY,0BAE3BmG,EAAMV,SAAS9F,EAExB,CAEA,OAAO,CACT,CACAK,EAAaA,EAAW6E,aAC1B,CAKF,OAAO,CACT,CAaIyB,CAAuB3G,GAK3B,EAEM4G,EAAiC,SAAU1F,EAASlB,GACxD,QACEmD,EAAmBnD,IACnBuC,EAAYvC,GAAQ,IACnBoG,EAAgClF,EAASlB,GAK9C,EAEM6G,EAA4B,SAAUC,GAC1C,IAAMrE,EAAWH,SAASwE,EAAe1G,aAAa,YAAa,IACnE,SAAIiC,MAAMI,IAAaA,GAAY,EAMrC,EAMMsE,EAAc,SAAdA,EAAwBrG,GAC5B,IAAMsG,EAAmB,GACnBC,EAAmB,GAqBzB,OApBAvG,EAAWwG,SAAQ,SAAUR,EAAMtC,GACjC,IAAM+C,IAAYT,EAAK5E,YACjBnC,EAAUwH,EAAUT,EAAK5E,YAAc4E,EACvCU,EAlUmB,SAAUpH,EAAMmH,GAC3C,IAAM1E,EAAWF,EAAYvC,GAE7B,OAAIyC,EAAW,GAAK0E,IAAY/E,EAAYpC,GACnC,EAGFyC,CACT,CA0T8B4E,CAAqB1H,EAASwH,GAClDlG,EAAWkG,EAAUJ,EAAYL,EAAKhG,YAAcf,EAChC,IAAtByH,EACFD,EACIH,EAAiBnF,KAAIhB,MAArBmG,EAAyB/F,GACzB+F,EAAiBnF,KAAKlC,GAE1BsH,EAAiBpF,KAAK,CACpBoB,cAAemB,EACf3B,SAAU2E,EACVV,KAAMA,EACNS,QAASA,EACTG,QAASrG,GAGf,IAEOgG,EACJM,KAAKzE,GACL0E,QAAO,SAACC,EAAKC,GAIZ,OAHAA,EAASP,QACLM,EAAI5F,KAAIhB,MAAR4G,EAAYC,EAASJ,SACrBG,EAAI5F,KAAK6F,EAASJ,SACfG,CACR,GAAE,IACFE,OAAOX,EACZ,EAEMY,EAAW,SAAUC,EAAW3G,GAGpC,IAAIR,EAmBJ,OAjBEA,GAJFQ,EAAUA,GAAW,IAGTe,cACGjB,EACX,CAAC6G,GACD3G,EAAQV,iBACR,CACEC,OAAQmG,EAA+BkB,KAAK,KAAM5G,GAClDU,SAAS,EACTK,cAAef,EAAQe,cACvBE,iBAAkB0E,IAITvG,EACXuH,EACA3G,EAAQV,iBACRoG,EAA+BkB,KAAK,KAAM5G,IAGvC6F,EAAYrG,EACrB,EAEMqH,EAAY,SAAUF,EAAW3G,GAsBrC,OArBAA,EAAUA,GAAW,IAGTe,cACGjB,EACX,CAAC6G,GACD3G,EAAQV,iBACR,CACEC,OAAQ2F,EAAgC0B,KAAK,KAAM5G,GACnDU,SAAS,EACTK,cAAef,EAAQe,gBAId3B,EACXuH,EACA3G,EAAQV,iBACR4F,EAAgC0B,KAAK,KAAM5G,GAKjD,EAEM8G,EAAa,SAAUhI,EAAMkB,GAEjC,GADAA,EAAUA,GAAW,IAChBlB,EACH,MAAM,IAAIwC,MAAM,oBAElB,OAA8C,IAA1ClD,EAAQO,KAAKG,EAAMd,IAGhB0H,EAA+B1F,EAASlB,EACjD,EAEMiI,EAA6ChJ,EAChD0I,OAAO,UACPxI,KAAK,KAEF+I,EAAc,SAAUlI,EAAMkB,GAElC,GADAA,EAAUA,GAAW,IAChBlB,EACH,MAAM,IAAIwC,MAAM,oBAElB,OAAuD,IAAnDlD,EAAQO,KAAKG,EAAMiI,IAGhB7B,EAAgClF,EAASlB,EAClD"}