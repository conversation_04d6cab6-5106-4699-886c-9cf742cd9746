function _typeof(o) {"@babel/helpers - typeof";return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, "string");return "symbol" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if ("object" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || "default");if ("object" != _typeof(i)) return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return ("string" === r ? String : Number)(t);}(function (_window$dateFns) {var __defProp = Object.defineProperty;
  var __export = function __export(target, all) {
    for (var name in all)
    __defProp(target, name, {
      get: all[name],
      enumerable: true,
      configurable: true,
      set: function set(newValue) {return all[name] = function () {return newValue;};}
    });
  };

  // lib/locale/de/_lib/formatDistance.mjs
  var formatDistanceLocale = {
    lessThanXSeconds: {
      standalone: {
        one: "weniger als 1 Sekunde",
        other: "weniger als {{count}} Sekunden"
      },
      withPreposition: {
        one: "weniger als 1 Sekunde",
        other: "weniger als {{count}} Sekunden"
      }
    },
    xSeconds: {
      standalone: {
        one: "1 Sekunde",
        other: "{{count}} Sekunden"
      },
      withPreposition: {
        one: "1 Sekunde",
        other: "{{count}} Sekunden"
      }
    },
    halfAMinute: {
      standalone: "eine halbe Minute",
      withPreposition: "einer halben Minute"
    },
    lessThanXMinutes: {
      standalone: {
        one: "weniger als 1 Minute",
        other: "weniger als {{count}} Minuten"
      },
      withPreposition: {
        one: "weniger als 1 Minute",
        other: "weniger als {{count}} Minuten"
      }
    },
    xMinutes: {
      standalone: {
        one: "1 Minute",
        other: "{{count}} Minuten"
      },
      withPreposition: {
        one: "1 Minute",
        other: "{{count}} Minuten"
      }
    },
    aboutXHours: {
      standalone: {
        one: "etwa 1 Stunde",
        other: "etwa {{count}} Stunden"
      },
      withPreposition: {
        one: "etwa 1 Stunde",
        other: "etwa {{count}} Stunden"
      }
    },
    xHours: {
      standalone: {
        one: "1 Stunde",
        other: "{{count}} Stunden"
      },
      withPreposition: {
        one: "1 Stunde",
        other: "{{count}} Stunden"
      }
    },
    xDays: {
      standalone: {
        one: "1 Tag",
        other: "{{count}} Tage"
      },
      withPreposition: {
        one: "1 Tag",
        other: "{{count}} Tagen"
      }
    },
    aboutXWeeks: {
      standalone: {
        one: "etwa 1 Woche",
        other: "etwa {{count}} Wochen"
      },
      withPreposition: {
        one: "etwa 1 Woche",
        other: "etwa {{count}} Wochen"
      }
    },
    xWeeks: {
      standalone: {
        one: "1 Woche",
        other: "{{count}} Wochen"
      },
      withPreposition: {
        one: "1 Woche",
        other: "{{count}} Wochen"
      }
    },
    aboutXMonths: {
      standalone: {
        one: "etwa 1 Monat",
        other: "etwa {{count}} Monate"
      },
      withPreposition: {
        one: "etwa 1 Monat",
        other: "etwa {{count}} Monaten"
      }
    },
    xMonths: {
      standalone: {
        one: "1 Monat",
        other: "{{count}} Monate"
      },
      withPreposition: {
        one: "1 Monat",
        other: "{{count}} Monaten"
      }
    },
    aboutXYears: {
      standalone: {
        one: "etwa 1 Jahr",
        other: "etwa {{count}} Jahre"
      },
      withPreposition: {
        one: "etwa 1 Jahr",
        other: "etwa {{count}} Jahren"
      }
    },
    xYears: {
      standalone: {
        one: "1 Jahr",
        other: "{{count}} Jahre"
      },
      withPreposition: {
        one: "1 Jahr",
        other: "{{count}} Jahren"
      }
    },
    overXYears: {
      standalone: {
        one: "mehr als 1 Jahr",
        other: "mehr als {{count}} Jahre"
      },
      withPreposition: {
        one: "mehr als 1 Jahr",
        other: "mehr als {{count}} Jahren"
      }
    },
    almostXYears: {
      standalone: {
        one: "fast 1 Jahr",
        other: "fast {{count}} Jahre"
      },
      withPreposition: {
        one: "fast 1 Jahr",
        other: "fast {{count}} Jahren"
      }
    }
  };
  var formatDistance = function formatDistance(token, count, options) {
    var result;
    var tokenValue = options !== null && options !== void 0 && options.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;
    if (typeof tokenValue === "string") {
      result = tokenValue;
    } else if (count === 1) {
      result = tokenValue.one;
    } else {
      result = tokenValue.other.replace("{{count}}", String(count));
    }
    if (options !== null && options !== void 0 && options.addSuffix) {
      if (options.comparison && options.comparison > 0) {
        return "in " + result;
      } else {
        return "vor " + result;
      }
    }
    return result;
  };

  // lib/locale/_lib/buildFormatLongFn.mjs
  function buildFormatLongFn(args) {
    return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var width = options.width ? String(options.width) : args.defaultWidth;
      var format = args.formats[width] || args.formats[args.defaultWidth];
      return format;
    };
  }

  // lib/locale/de/_lib/formatLong.mjs
  var dateFormats = {
    full: "EEEE, do MMMM y",
    long: "do MMMM y",
    medium: "do MMM y",
    short: "dd.MM.y"
  };
  var timeFormats = {
    full: "HH:mm:ss zzzz",
    long: "HH:mm:ss z",
    medium: "HH:mm:ss",
    short: "HH:mm"
  };
  var dateTimeFormats = {
    full: "{{date}} 'um' {{time}}",
    long: "{{date}} 'um' {{time}}",
    medium: "{{date}} {{time}}",
    short: "{{date}} {{time}}"
  };
  var formatLong = {
    date: buildFormatLongFn({
      formats: dateFormats,
      defaultWidth: "full"
    }),
    time: buildFormatLongFn({
      formats: timeFormats,
      defaultWidth: "full"
    }),
    dateTime: buildFormatLongFn({
      formats: dateTimeFormats,
      defaultWidth: "full"
    })
  };

  // lib/locale/de/_lib/formatRelative.mjs
  var formatRelativeLocale = {
    lastWeek: "'letzten' eeee 'um' p",
    yesterday: "'gestern um' p",
    today: "'heute um' p",
    tomorrow: "'morgen um' p",
    nextWeek: "eeee 'um' p",
    other: "P"
  };
  var formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};

  // lib/locale/_lib/buildLocalizeFn.mjs
  function buildLocalizeFn(args) {
    return function (value, options) {
      var context = options !== null && options !== void 0 && options.context ? String(options.context) : "standalone";
      var valuesArray;
      if (context === "formatting" && args.formattingValues) {
        var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
        var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;
        valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];
      } else {
        var _defaultWidth = args.defaultWidth;
        var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;
        valuesArray = args.values[_width] || args.values[_defaultWidth];
      }
      var index = args.argumentCallback ? args.argumentCallback(value) : value;
      return valuesArray[index];
    };
  }

  // lib/locale/de/_lib/localize.mjs
  var eraValues = {
    narrow: ["v.Chr.", "n.Chr."],
    abbreviated: ["v.Chr.", "n.Chr."],
    wide: ["vor Christus", "nach Christus"]
  };
  var quarterValues = {
    narrow: ["1", "2", "3", "4"],
    abbreviated: ["Q1", "Q2", "Q3", "Q4"],
    wide: ["1. Quartal", "2. Quartal", "3. Quartal", "4. Quartal"]
  };
  var monthValues = {
    narrow: ["J", "F", "M", "A", "M", "J", "J", "A", "S", "O", "N", "D"],
    abbreviated: [
    "Jan",
    "Feb",
    "M\xE4r",
    "Apr",
    "Mai",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Okt",
    "Nov",
    "Dez"],

    wide: [
    "Januar",
    "Februar",
    "M\xE4rz",
    "April",
    "Mai",
    "Juni",
    "Juli",
    "August",
    "September",
    "Oktober",
    "November",
    "Dezember"]

  };
  var formattingMonthValues = {
    narrow: monthValues.narrow,
    abbreviated: [
    "Jan.",
    "Feb.",
    "M\xE4rz",
    "Apr.",
    "Mai",
    "Juni",
    "Juli",
    "Aug.",
    "Sep.",
    "Okt.",
    "Nov.",
    "Dez."],

    wide: monthValues.wide
  };
  var dayValues = {
    narrow: ["S", "M", "D", "M", "D", "F", "S"],
    short: ["So", "Mo", "Di", "Mi", "Do", "Fr", "Sa"],
    abbreviated: ["So.", "Mo.", "Di.", "Mi.", "Do.", "Fr.", "Sa."],
    wide: [
    "Sonntag",
    "Montag",
    "Dienstag",
    "Mittwoch",
    "Donnerstag",
    "Freitag",
    "Samstag"]

  };
  var dayPeriodValues = {
    narrow: {
      am: "vm.",
      pm: "nm.",
      midnight: "Mitternacht",
      noon: "Mittag",
      morning: "Morgen",
      afternoon: "Nachm.",
      evening: "Abend",
      night: "Nacht"
    },
    abbreviated: {
      am: "vorm.",
      pm: "nachm.",
      midnight: "Mitternacht",
      noon: "Mittag",
      morning: "Morgen",
      afternoon: "Nachmittag",
      evening: "Abend",
      night: "Nacht"
    },
    wide: {
      am: "vormittags",
      pm: "nachmittags",
      midnight: "Mitternacht",
      noon: "Mittag",
      morning: "Morgen",
      afternoon: "Nachmittag",
      evening: "Abend",
      night: "Nacht"
    }
  };
  var formattingDayPeriodValues = {
    narrow: {
      am: "vm.",
      pm: "nm.",
      midnight: "Mitternacht",
      noon: "Mittag",
      morning: "morgens",
      afternoon: "nachm.",
      evening: "abends",
      night: "nachts"
    },
    abbreviated: {
      am: "vorm.",
      pm: "nachm.",
      midnight: "Mitternacht",
      noon: "Mittag",
      morning: "morgens",
      afternoon: "nachmittags",
      evening: "abends",
      night: "nachts"
    },
    wide: {
      am: "vormittags",
      pm: "nachmittags",
      midnight: "Mitternacht",
      noon: "Mittag",
      morning: "morgens",
      afternoon: "nachmittags",
      evening: "abends",
      night: "nachts"
    }
  };
  var ordinalNumber = function ordinalNumber(dirtyNumber) {
    var number = Number(dirtyNumber);
    return number + ".";
  };
  var localize = {
    ordinalNumber: ordinalNumber,
    era: buildLocalizeFn({
      values: eraValues,
      defaultWidth: "wide"
    }),
    quarter: buildLocalizeFn({
      values: quarterValues,
      defaultWidth: "wide",
      argumentCallback: function argumentCallback(quarter) {return quarter - 1;}
    }),
    month: buildLocalizeFn({
      values: monthValues,
      formattingValues: formattingMonthValues,
      defaultWidth: "wide"
    }),
    day: buildLocalizeFn({
      values: dayValues,
      defaultWidth: "wide"
    }),
    dayPeriod: buildLocalizeFn({
      values: dayPeriodValues,
      defaultWidth: "wide",
      formattingValues: formattingDayPeriodValues,
      defaultFormattingWidth: "wide"
    })
  };

  // lib/locale/_lib/buildMatchFn.mjs
  function buildMatchFn(args) {
    return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var width = options.width;
      var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];
      var matchResult = string.match(matchPattern);
      if (!matchResult) {
        return null;
      }
      var matchedString = matchResult[0];
      var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];
      var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});
      var value;
      value = args.valueCallback ? args.valueCallback(key) : key;
      value = options.valueCallback ? options.valueCallback(value) : value;
      var rest = string.slice(matchedString.length);
      return { value: value, rest: rest };
    };
  }
  var findKey = function findKey(object, predicate) {
    for (var key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {
        return key;
      }
    }
    return;
  };
  var findIndex = function findIndex(array, predicate) {
    for (var key = 0; key < array.length; key++) {
      if (predicate(array[key])) {
        return key;
      }
    }
    return;
  };

  // lib/locale/_lib/buildMatchPatternFn.mjs
  function buildMatchPatternFn(args) {
    return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var matchResult = string.match(args.matchPattern);
      if (!matchResult)
      return null;
      var matchedString = matchResult[0];
      var parseResult = string.match(args.parsePattern);
      if (!parseResult)
      return null;
      var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];
      value = options.valueCallback ? options.valueCallback(value) : value;
      var rest = string.slice(matchedString.length);
      return { value: value, rest: rest };
    };
  }

  // lib/locale/de/_lib/match.mjs
  var matchOrdinalNumberPattern = /^(\d+)(\.)?/i;
  var parseOrdinalNumberPattern = /\d+/i;
  var matchEraPatterns = {
    narrow: /^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,
    abbreviated: /^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,
    wide: /^(vor Christus|vor unserer Zeitrechnung|nach Christus|unserer Zeitrechnung)/i
  };
  var parseEraPatterns = {
    any: [/^v/i, /^n/i]
  };
  var matchQuarterPatterns = {
    narrow: /^[1234]/i,
    abbreviated: /^q[1234]/i,
    wide: /^[1234](\.)? Quartal/i
  };
  var parseQuarterPatterns = {
    any: [/1/i, /2/i, /3/i, /4/i]
  };
  var matchMonthPatterns = {
    narrow: /^[jfmasond]/i,
    abbreviated: /^(j[aä]n|feb|mär[z]?|apr|mai|jun[i]?|jul[i]?|aug|sep|okt|nov|dez)\.?/i,
    wide: /^(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)/i
  };
  var parseMonthPatterns = {
    narrow: [
    /^j/i,
    /^f/i,
    /^m/i,
    /^a/i,
    /^m/i,
    /^j/i,
    /^j/i,
    /^a/i,
    /^s/i,
    /^o/i,
    /^n/i,
    /^d/i],

    any: [
    /^j[aä]/i,
    /^f/i,
    /^mär/i,
    /^ap/i,
    /^mai/i,
    /^jun/i,
    /^jul/i,
    /^au/i,
    /^s/i,
    /^o/i,
    /^n/i,
    /^d/i]

  };
  var matchDayPatterns = {
    narrow: /^[smdmf]/i,
    short: /^(so|mo|di|mi|do|fr|sa)/i,
    abbreviated: /^(son?|mon?|die?|mit?|don?|fre?|sam?)\.?/i,
    wide: /^(sonntag|montag|dienstag|mittwoch|donnerstag|freitag|samstag)/i
  };
  var parseDayPatterns = {
    any: [/^so/i, /^mo/i, /^di/i, /^mi/i, /^do/i, /^f/i, /^sa/i]
  };
  var matchDayPeriodPatterns = {
    narrow: /^(vm\.?|nm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,
    abbreviated: /^(vorm\.?|nachm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,
    wide: /^(vormittags|nachmittags|Mitternacht|Mittag|morgens|nachmittags|abends|nachts)/i
  };
  var parseDayPeriodPatterns = {
    any: {
      am: /^v/i,
      pm: /^n/i,
      midnight: /^Mitte/i,
      noon: /^Mitta/i,
      morning: /morgens/i,
      afternoon: /nachmittags/i,
      evening: /abends/i,
      night: /nachts/i
    }
  };
  var match = {
    ordinalNumber: buildMatchPatternFn({
      matchPattern: matchOrdinalNumberPattern,
      parsePattern: parseOrdinalNumberPattern,
      valueCallback: function valueCallback(value) {return parseInt(value);}
    }),
    era: buildMatchFn({
      matchPatterns: matchEraPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseEraPatterns,
      defaultParseWidth: "any"
    }),
    quarter: buildMatchFn({
      matchPatterns: matchQuarterPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseQuarterPatterns,
      defaultParseWidth: "any",
      valueCallback: function valueCallback(index) {return index + 1;}
    }),
    month: buildMatchFn({
      matchPatterns: matchMonthPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseMonthPatterns,
      defaultParseWidth: "any"
    }),
    day: buildMatchFn({
      matchPatterns: matchDayPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseDayPatterns,
      defaultParseWidth: "any"
    }),
    dayPeriod: buildMatchFn({
      matchPatterns: matchDayPeriodPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseDayPeriodPatterns,
      defaultParseWidth: "any"
    })
  };

  // lib/locale/de.mjs
  var de = {
    code: "de",
    formatDistance: formatDistance,
    formatLong: formatLong,
    formatRelative: formatRelative,
    localize: localize,
    match: match,
    options: {
      weekStartsOn: 1,
      firstWeekContainsDate: 4
    }
  };

  // lib/locale/de/cdn.js
  window.dateFns = _objectSpread(_objectSpread({},
  window.dateFns), {}, {
    locale: _objectSpread(_objectSpread({}, (_window$dateFns =
    window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {
      de: de }) });



  //# debugId=D31C30858CFE01DD64756e2164756e21
})();

//# sourceMappingURL=cdn.js.map