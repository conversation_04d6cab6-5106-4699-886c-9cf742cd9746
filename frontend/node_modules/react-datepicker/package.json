{"author": "HackerOne", "name": "react-datepicker", "description": "A simple and reusable datepicker component for React", "version": "8.4.0", "license": "MIT", "homepage": "https://github.com/Hacker0x01/react-datepicker", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.es.js", "unpkg": "dist/react-datepicker.min.js", "style": "dist/react-datepicker.min.css", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.es.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./dist/*.css": "./dist/*.css", "./src/stylesheets/*.scss": "./src/stylesheets/*.scss"}, "files": ["*.md", "dist", "lib", "es", "src/stylesheets"], "sideEffects": ["**/*.css"], "keywords": ["react", "datepicker", "calendar", "date", "react-component"], "repository": {"type": "git", "url": "git://github.com/Hacker0x01/react-datepicker.git"}, "bugs": {"url": "https://github.com/Hacker0x01/react-datepicker/issues"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/helpers": "^7.26.7", "@babel/plugin-external-helpers": "^7.25.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.26.7", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@eslint/js": "^9.19.0", "@react-docgen/cli": "^3.0.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "14.6.1", "@types/eslint": "^9.6.1", "@types/jest": "^29.5.14", "@types/node": "22.14.1", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "axe-core": "^4.10.2", "babel-jest": "^29.7.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "core-js": "^3.40.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-unused-imports": "^4.1.4", "husky": "9.1.7", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.4.3", "lodash": "^4.17.21", "prettier": "^3.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "rollup": "^4.32.1", "rollup-plugin-filesize": "^10.0.0", "sass": "1.89.0", "slugify": "^1.6.6", "stylelint": "^16.14.1", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-scss": "^6.11.0", "ts-jest": "^29.2.5", "tslib": "^2.8.1", "typescript": "^5.7.3", "typescript-eslint": "^8.22.0"}, "peerDependencies": {"react": "^16.9.0 || ^17 || ^18 || ^19 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17 || ^18 || ^19 || ^19.0.0-rc"}, "dependencies": {"@floating-ui/react": "^0.27.3", "clsx": "^2.1.1", "date-fns": "^4.1.0"}, "scripts": {"eslint": "eslint ./src", "precommit": "lint-staged --allow-empty", "sass-lint": "stylelint 'src/stylesheets/*.scss'", "lint": "yarn run eslint && yarn run sass-lint", "prettier": "prettier --write '**/*.{js,jsx,ts,tsx}'", "prettier:check": "prettier --check '**/*.{js,jsx,ts,tsx}'", "start": "yarn --cwd docs-site install && yarn --cwd docs-site start", "test": "NODE_ENV=test jest", "test:ci": "NODE_ENV=test jest --ci --coverage", "test:watch": "NODE_OPTIONS=--openssl-legacy-provider NODE_ENV=test jest --watch", "build": "NODE_ENV=production yarn run build:src && NODE_ENV=production yarn run css:prod && NODE_ENV=production yarn run css:modules:prod && NODE_ENV=production yarn run css:dev && NODE_ENV=production yarn run css:modules:dev", "build-dev": "NODE_ENV=development yarn run js:dev && NODE_ENV=development yarn run css:dev && NODE_ENV=development yarn run css:modules:dev", "css:prod": "sass --style compressed src/stylesheets/datepicker.scss > dist/react-datepicker.min.css", "css:modules:prod": "sass --style compressed src/stylesheets/datepicker-cssmodules.scss | tee dist/react-datepicker-cssmodules.min.css dist/react-datepicker-min.module.css", "css:dev": "sass --style expanded src/stylesheets/datepicker.scss > dist/react-datepicker.css", "css:modules:dev": "sass --style expanded src/stylesheets/datepicker-cssmodules.scss | tee dist/react-datepicker-cssmodules.css dist/react-datepicker.module.css", "type-check": "tsc --project tsconfig.build.json --noEmit", "type-check:watch": "npm run type-check -- --watch", "build:src": "rollup -c", "js:dev": "rollup -cw", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write", "git add"]}, "packageManager": "yarn@4.7.0"}