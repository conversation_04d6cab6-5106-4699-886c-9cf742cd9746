#!/bin/bash

# CDR Site Monitor - Deployment Test Script
# This script tests the deployment process without actually deploying

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Test functions
test_prerequisites() {
    print_status "Testing prerequisites..."
    
    local tests_passed=0
    local tests_total=5
    
    # Test Node.js
    if command -v node >/dev/null 2>&1; then
        print_success "Node.js is installed ($(node --version))"
        ((tests_passed++))
    else
        print_error "Node.js is not installed"
    fi
    
    # Test npm
    if command -v npm >/dev/null 2>&1; then
        print_success "npm is installed ($(npm --version))"
        ((tests_passed++))
    else
        print_error "npm is not installed"
    fi
    
    # Test Ruby
    if command -v ruby >/dev/null 2>&1; then
        print_success "Ruby is installed ($(ruby --version | cut -d' ' -f2))"
        ((tests_passed++))
    else
        print_error "Ruby is not installed"
    fi
    
    # Test Bundler
    if command -v bundle >/dev/null 2>&1; then
        print_success "Bundler is installed ($(bundle --version))"
        ((tests_passed++))
    else
        print_error "Bundler is not installed"
    fi
    
    # Test Rails
    if command -v rails >/dev/null 2>&1; then
        print_success "Rails is installed ($(rails --version))"
        ((tests_passed++))
    else
        print_error "Rails is not installed"
    fi
    
    echo "Prerequisites: $tests_passed/$tests_total passed"
    return $((tests_total - tests_passed))
}

test_project_structure() {
    print_status "Testing project structure..."
    
    local tests_passed=0
    local tests_total=6
    
    # Test main directories
    if [ -d "$PROJECT_ROOT/frontend" ]; then
        print_success "Frontend directory exists"
        ((tests_passed++))
    else
        print_error "Frontend directory missing"
    fi
    
    if [ -d "$PROJECT_ROOT/app" ]; then
        print_success "Rails app directory exists"
        ((tests_passed++))
    else
        print_error "Rails app directory missing"
    fi
    
    # Test key files
    if [ -f "$PROJECT_ROOT/frontend/package.json" ]; then
        print_success "Frontend package.json exists"
        ((tests_passed++))
    else
        print_error "Frontend package.json missing"
    fi
    
    if [ -f "$PROJECT_ROOT/Gemfile" ]; then
        print_success "Rails Gemfile exists"
        ((tests_passed++))
    else
        print_error "Rails Gemfile missing"
    fi
    
    if [ -f "$PROJECT_ROOT/scripts/deploy.sh" ]; then
        print_success "Deployment script exists"
        ((tests_passed++))
    else
        print_error "Deployment script missing"
    fi
    
    if [ -f "$PROJECT_ROOT/app/controllers/frontend_controller.rb" ]; then
        print_success "Frontend controller exists"
        ((tests_passed++))
    else
        print_error "Frontend controller missing"
    fi
    
    echo "Project structure: $tests_passed/$tests_total passed"
    return $((tests_total - tests_passed))
}

test_configuration_files() {
    print_status "Testing configuration files..."
    
    local tests_passed=0
    local tests_total=4
    
    # Test Next.js config
    if [ -f "$PROJECT_ROOT/frontend/next.config.js" ]; then
        print_success "Next.js configuration exists"
        ((tests_passed++))
    else
        print_error "Next.js configuration missing"
    fi
    
    # Test Rails routes
    if grep -q "frontend#index" "$PROJECT_ROOT/config/routes.rb"; then
        print_success "Frontend routes configured"
        ((tests_passed++))
    else
        print_error "Frontend routes not configured"
    fi
    
    # Test production config
    if grep -q "public_file_server.enabled = true" "$PROJECT_ROOT/config/environments/production.rb"; then
        print_success "Production static file serving enabled"
        ((tests_passed++))
    else
        print_error "Production static file serving not configured"
    fi
    
    # Test package.json scripts
    if grep -q '"export"' "$PROJECT_ROOT/frontend/package.json"; then
        print_success "Frontend export script configured"
        ((tests_passed++))
    else
        print_error "Frontend export script missing"
    fi
    
    echo "Configuration: $tests_passed/$tests_total passed"
    return $((tests_total - tests_passed))
}

# Main test execution
main() {
    print_status "CDR Site Monitor - Deployment Test"
    print_status "=================================="
    echo
    
    local total_failures=0
    
    # Run tests
    test_prerequisites
    total_failures=$((total_failures + $?))
    echo
    
    test_project_structure
    total_failures=$((total_failures + $?))
    echo
    
    test_configuration_files
    total_failures=$((total_failures + $?))
    echo
    
    # Summary
    if [ $total_failures -eq 0 ]; then
        print_success "All tests passed! Deployment should work correctly."
        print_status "You can now run: ./scripts/deploy.sh"
    else
        print_error "$total_failures test(s) failed. Please fix the issues before deploying."
        exit 1
    fi
}

# Script entry point
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
