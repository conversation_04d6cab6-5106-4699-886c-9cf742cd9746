#!/bin/bash

# CDR Site Monitor - Unified Deployment Script
# This script builds the Next.js frontend and integrates it with the Rails API
# to create a single unified service.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DEFAULT_PORT=3000
DEFAULT_ENV="production"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to prompt user for input
prompt_user() {
    local prompt_text="$1"
    local default_value="$2"
    local user_input
    
    echo -n -e "${YELLOW}$prompt_text${NC}"
    if [ -n "$default_value" ]; then
        echo -n " (default: $default_value): "
    else
        echo -n ": "
    fi
    
    read user_input
    echo "${user_input:-$default_value}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    local missing_deps=()
    
    if ! command_exists node; then
        missing_deps+=("Node.js")
    fi
    
    if ! command_exists npm; then
        missing_deps+=("npm")
    fi
    
    if ! command_exists ruby; then
        missing_deps+=("Ruby")
    fi
    
    if ! command_exists bundle; then
        missing_deps+=("Bundler")
    fi
    
    if ! command_exists rails; then
        missing_deps+=("Rails")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        print_error "Please install the missing dependencies and try again."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Function to get user configuration
get_user_config() {
    print_status "Getting deployment configuration..."
    
    # Get port configuration
    API_PORT=$(prompt_user "Enter the port for the Rails server" "$DEFAULT_PORT")
    
    # Get environment
    RAILS_ENV=$(prompt_user "Enter the Rails environment" "$DEFAULT_ENV")
    
    # Get database configuration
    if [ "$RAILS_ENV" = "production" ]; then
        DB_MIGRATE=$(prompt_user "Run database migrations? (y/n)" "y")
        DB_SEED=$(prompt_user "Run database seeding? (y/n)" "n")
    else
        DB_MIGRATE="y"
        DB_SEED="y"
    fi
    
    # Confirm configuration
    echo
    print_status "Deployment Configuration:"
    echo "  Rails Environment: $RAILS_ENV"
    echo "  API Port: $API_PORT"
    echo "  Database Migration: $DB_MIGRATE"
    echo "  Database Seeding: $DB_SEED"
    echo
    
    CONFIRM=$(prompt_user "Proceed with this configuration? (y/n)" "y")
    if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
        print_warning "Deployment cancelled by user"
        exit 0
    fi
}

# Function to backup current deployment
backup_current_deployment() {
    print_status "Creating backup of current deployment..."
    
    local backup_dir="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup public directory if it exists
    if [ -d "$PROJECT_ROOT/public/assets" ]; then
        cp -r "$PROJECT_ROOT/public/assets" "$backup_dir/" 2>/dev/null || true
    fi
    
    # Backup database
    if [ -f "$PROJECT_ROOT/db/development.sqlite3" ]; then
        cp "$PROJECT_ROOT/db/development.sqlite3" "$backup_dir/" 2>/dev/null || true
    fi
    
    echo "$backup_dir" > "$PROJECT_ROOT/.last_backup"
    print_success "Backup created at: $backup_dir"
}

# Function to configure frontend API URL
configure_frontend_api() {
    print_status "Configuring frontend API URL..."
    
    local api_config_file="$PROJECT_ROOT/frontend/src/lib/api.ts"
    local api_url="http://localhost:$API_PORT/api"
    
    # Create a temporary file with the updated API URL
    sed "s|baseURL: '[^']*'|baseURL: '$api_url'|g" "$api_config_file" > "$api_config_file.tmp"
    mv "$api_config_file.tmp" "$api_config_file"
    
    print_success "Frontend API URL configured to: $api_url"
}

# Function to build frontend
build_frontend() {
    print_status "Building Next.js frontend..."
    
    cd "$PROJECT_ROOT/frontend"
    
    # Install dependencies
    print_status "Installing frontend dependencies..."
    npm ci --production=false
    
    # Build the application
    print_status "Building frontend for production..."
    npm run build
    
    # Export static files
    print_status "Exporting static files..."
    npm run export 2>/dev/null || {
        print_warning "Export command not available, using build output directly"
    }
    
    cd "$PROJECT_ROOT"
    print_success "Frontend build completed"
}

# Function to integrate frontend with Rails
integrate_frontend_with_rails() {
    print_status "Integrating frontend with Rails application..."
    
    # Clean existing assets
    rm -rf "$PROJECT_ROOT/public/assets"
    rm -rf "$PROJECT_ROOT/public/_next"
    rm -f "$PROJECT_ROOT/public/index.html"
    
    # Copy built frontend assets
    local frontend_build_dir="$PROJECT_ROOT/frontend/out"
    if [ ! -d "$frontend_build_dir" ]; then
        frontend_build_dir="$PROJECT_ROOT/frontend/.next"
    fi
    
    if [ -d "$frontend_build_dir" ]; then
        # Copy static assets
        if [ -d "$frontend_build_dir/_next" ]; then
            cp -r "$frontend_build_dir/_next" "$PROJECT_ROOT/public/"
        fi
        
        # Copy other static files
        find "$frontend_build_dir" -maxdepth 1 -type f \( -name "*.html" -o -name "*.js" -o -name "*.css" -o -name "*.ico" -o -name "*.png" -o -name "*.jpg" -o -name "*.svg" \) -exec cp {} "$PROJECT_ROOT/public/" \;
        
        print_success "Frontend assets integrated with Rails"
    else
        print_error "Frontend build directory not found"
        exit 1
    fi
}

# Function to configure Rails for frontend serving
configure_rails_frontend_serving() {
    print_status "Configuring Rails to serve frontend..."

    # Create the frontend controller if it doesn't exist
    local frontend_controller="$PROJECT_ROOT/app/controllers/frontend_controller.rb"
    if [ ! -f "$frontend_controller" ]; then
        cat > "$frontend_controller" << 'EOF'
class FrontendController < ApplicationController
  def index
    render file: Rails.public_path.join('index.html'), layout: false
  end
end
EOF
    fi

    print_success "Rails frontend serving configured"
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."

    cd "$PROJECT_ROOT"

    # Set environment
    export RAILS_ENV="$RAILS_ENV"

    # Create database if it doesn't exist
    bundle exec rails db:create 2>/dev/null || true

    # Run migrations if requested
    if [ "$DB_MIGRATE" = "y" ] || [ "$DB_MIGRATE" = "Y" ]; then
        print_status "Running database migrations..."
        bundle exec rails db:migrate
        print_success "Database migrations completed"
    fi

    # Run seeding if requested
    if [ "$DB_SEED" = "y" ] || [ "$DB_SEED" = "Y" ]; then
        print_status "Seeding database..."
        bundle exec rails db:seed
        print_success "Database seeding completed"
    fi
}

# Function to install Rails dependencies
install_rails_dependencies() {
    print_status "Installing Rails dependencies..."

    cd "$PROJECT_ROOT"
    bundle install --without development test

    print_success "Rails dependencies installed"
}

# Function to precompile Rails assets
precompile_rails_assets() {
    print_status "Precompiling Rails assets..."

    cd "$PROJECT_ROOT"
    export RAILS_ENV="$RAILS_ENV"

    bundle exec rails assets:precompile

    print_success "Rails assets precompiled"
}

# Function to perform health check
perform_health_check() {
    print_status "Performing health check..."

    # Start Rails server in background
    cd "$PROJECT_ROOT"
    export RAILS_ENV="$RAILS_ENV"

    bundle exec rails server -p "$API_PORT" -d

    # Wait for server to start
    sleep 5

    # Check API health
    local api_health_url="http://localhost:$API_PORT/up"
    local max_attempts=10
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -s "$api_health_url" > /dev/null 2>&1; then
            print_success "Health check passed - API is responding"

            # Stop the background server
            pkill -f "rails server" || true
            return 0
        fi

        print_status "Health check attempt $attempt/$max_attempts failed, retrying..."
        sleep 2
        ((attempt++))
    done

    print_error "Health check failed - API is not responding"
    pkill -f "rails server" || true
    return 1
}

# Function to rollback deployment
rollback_deployment() {
    print_error "Deployment failed, initiating rollback..."

    if [ -f "$PROJECT_ROOT/.last_backup" ]; then
        local backup_dir=$(cat "$PROJECT_ROOT/.last_backup")
        if [ -d "$backup_dir" ]; then
            print_status "Restoring from backup: $backup_dir"

            # Restore public assets
            if [ -d "$backup_dir/assets" ]; then
                rm -rf "$PROJECT_ROOT/public/assets"
                cp -r "$backup_dir/assets" "$PROJECT_ROOT/public/"
            fi

            # Restore database
            if [ -f "$backup_dir/development.sqlite3" ]; then
                cp "$backup_dir/development.sqlite3" "$PROJECT_ROOT/db/"
            fi

            print_success "Rollback completed"
        else
            print_warning "Backup directory not found, manual rollback may be required"
        fi
    else
        print_warning "No backup information found, manual rollback may be required"
    fi
}

# Function to start the application
start_application() {
    print_status "Starting CDR Site Monitor application..."

    cd "$PROJECT_ROOT"
    export RAILS_ENV="$RAILS_ENV"

    print_success "Application is ready to start!"
    print_status "To start the application, run:"
    echo "  cd $PROJECT_ROOT"
    echo "  RAILS_ENV=$RAILS_ENV bundle exec rails server -p $API_PORT"
    echo
    print_status "The application will be available at:"
    echo "  Frontend: http://localhost:$API_PORT"
    echo "  API: http://localhost:$API_PORT/api"
    echo

    # Ask if user wants to start now
    START_NOW=$(prompt_user "Start the application now? (y/n)" "y")
    if [ "$START_NOW" = "y" ] || [ "$START_NOW" = "Y" ]; then
        print_status "Starting application..."
        exec bundle exec rails server -p "$API_PORT"
    fi
}

# Main execution function
main() {
    print_status "CDR Site Monitor - Unified Deployment Script"
    print_status "============================================="
    echo

    # Trap errors for rollback
    trap 'rollback_deployment; exit 1' ERR

    # Check prerequisites
    check_prerequisites

    # Get user configuration
    get_user_config

    # Create backup
    backup_current_deployment

    # Configure frontend API URL
    configure_frontend_api

    # Build frontend
    build_frontend

    # Install Rails dependencies
    install_rails_dependencies

    # Integrate frontend with Rails
    integrate_frontend_with_rails

    # Configure Rails for frontend serving
    configure_rails_frontend_serving

    # Setup database
    setup_database

    # Precompile Rails assets
    precompile_rails_assets

    # Perform health check
    if perform_health_check; then
        print_success "Deployment completed successfully!"

        # Start application
        start_application
    else
        print_error "Deployment failed health check"
        exit 1
    fi
}

# Script entry point
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
