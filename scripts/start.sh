#!/bin/bash

# CDR Site Monitor - Start Script
# Simple script to start the unified application

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DEFAULT_PORT=3000
DEFAULT_ENV="production"

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Get port from command line or use default
PORT=${1:-$DEFAULT_PORT}
RAILS_ENV=${2:-$DEFAULT_ENV}

print_status "Starting CDR Site Monitor..."
print_status "Port: $PORT"
print_status "Environment: $RAILS_ENV"
echo

cd "$PROJECT_ROOT"

# Set environment variables
export RAILS_ENV="$RAILS_ENV"
export PORT="$PORT"

# Check if frontend is built
if [ ! -f "public/index.html" ]; then
    print_status "Frontend not found. Please run the deployment script first:"
    echo "  ./scripts/deploy.sh"
    exit 1
fi

print_success "Starting application on port $PORT..."
print_status "Frontend: http://localhost:$PORT"
print_status "API: http://localhost:$PORT/api"
echo
print_status "Press Ctrl+C to stop the application"
echo

# Start the Rails server
exec bundle exec rails server -p "$PORT" -e "$RAILS_ENV"
