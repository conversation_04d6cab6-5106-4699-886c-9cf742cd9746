class FrontendController < ApplicationController
  # Skip CSRF protection for frontend routes
  # skip_before_action :verify_authenticity_token

  def index
    # Serve the Next.js index.html file for all frontend routes
    render file: Rails.public_path.join("index.html"), layout: false
  rescue ActionView::MissingTemplate
    # Fallback if index.html is not found
    render json: {
      error: "Frontend not built",
      message: "Please run the deployment script to build the frontend"
    }, status: 503
  end
end
