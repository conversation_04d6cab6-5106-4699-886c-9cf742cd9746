class Api::HealthChecksController < ApplicationController
  before_action :set_site, only: [ :create ]
  before_action :set_health_check, only: [ :show ]

  def index
    if params[:site_id]
      # Site-specific health checks
      @site = Site.find(params[:site_id])
      @health_checks = @site.health_checks.recent
    else
      # All health checks
      @health_checks = HealthCheck.includes(:site).recent
    end

    # Apply date filtering if provided
    if params[:start_date].present?
      start_date = Time.parse(params[:start_date])
      @health_checks = @health_checks.where("created_at >= ?", start_date)
    end

    if params[:end_date].present?
      end_date = Time.parse(params[:end_date])
      @health_checks = @health_checks.where("created_at <= ?", end_date)
    end

    # Limit results if no date filter is applied
    @health_checks = @health_checks.limit(50) unless params[:start_date].present? || params[:end_date].present?

    render json: @health_checks
  end

  def show
    render json: @health_check
  end

  def create
    @health_check = @site.health_checks.new(health_check_params)

    if @health_check.save
      # Update site status based on health check
      @site.update(status: @health_check.status, last_check_at: Time.current, response_time: @health_check.response_time)
      render json: @health_check, status: :created
    else
      render json: { errors: @health_check.errors }, status: :unprocessable_entity
    end
  end

  private

  def set_site
    @site = Site.find(params[:site_id])
  end

  def set_health_check
    @health_check = HealthCheck.find(params[:id])
  end

  def health_check_params
    params.require(:health_check).permit(
      :status,
      :response_time,
      :jobs_done,
      :jobs_pending,
      :jobs_failed,
      :jobs_queued,
      :last_sync_at,
      :visitor_count
    )
  end
end
