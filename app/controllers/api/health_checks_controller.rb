class Api::HealthChecksController < ApplicationController
  before_action :set_site, only: [ :index, :create ]
  before_action :set_health_check, only: [ :show ]

  def index
    @health_checks = @site.health_checks.recent.limit(50)
    render json: @health_checks
  end

  def show
    render json: @health_check
  end

  def create
    @health_check = @site.health_checks.new(health_check_params)

    if @health_check.save
      # Update site status based on health check
      @site.update(status: @health_check.status, last_check_at: Time.current, response_time: @health_check.response_time)
      render json: @health_check, status: :created
    else
      render json: { errors: @health_check.errors }, status: :unprocessable_entity
    end
  end

  private

  def set_site
    @site = Site.find(params[:site_id])
  end

  def set_health_check
    @health_check = HealthCheck.find(params[:id])
  end

  def health_check_params
    params.require(:health_check).permit(
      :status,
      :response_time,
      :jobs_done,
      :jobs_pending,
      :jobs_failed,
      :jobs_queued,
      :last_sync_at,
      :visitor_count
    )
  end
end
