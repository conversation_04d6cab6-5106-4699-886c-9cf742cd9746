class Api::SitesController < ApplicationController
  before_action :set_site, only: [ :show, :update, :destroy, :failed_jobs ]

  def index
    @sites = Site.includes(:health_checks).all
    render json: @sites.map { |site| site_with_latest_health_check(site) }
  end

  def show
    render json: site_with_latest_health_check(@site)
  end

  def create
    @site = Site.new(site_params)

    if @site.save
      render json: site_with_latest_health_check(@site), status: :created
    else
      render json: { errors: @site.errors }, status: :unprocessable_entity
    end
  end

  def update
    if @site.update(site_params)
      render json: site_with_latest_health_check(@site)
    else
      render json: { errors: @site.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    @site.destroy
    head :no_content
  end

  def failed_jobs
    service = FailedJobsService.new(@site)
    failed_jobs = service.fetch_failed_jobs

    render json: {
      site_id: @site.id,
      site_name: @site.name,
      failed_jobs: failed_jobs,
      total_count: failed_jobs.size
    }
  rescue StandardError => e
    render json: {
      error: "Failed to fetch failed jobs: #{e.message}",
      site_id: @site.id,
      failed_jobs: [],
      total_count: 0
    }, status: :service_unavailable
  end

  private

  def set_site
    @site = Site.find(params[:id])
  end

  def site_params
    params.require(:site).permit(:name, :ip_address, :port)
  end

  def site_with_latest_health_check(site)
    site.as_json.merge(
      latest_health_check: site.latest_health_check&.as_json,
      health_checks_count: site.health_checks.count
    )
  end
end
