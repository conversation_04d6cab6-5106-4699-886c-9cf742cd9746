class Api::<PERSON>ertsController < ApplicationController
  before_action :set_site, only: [ :index, :create ]
  before_action :set_alert, only: [ :update, :destroy ]

  def index
    @alerts = @site.alerts
    render json: @alerts
  end

  def create
    @alert = @site.alerts.new(alert_params)

    if @alert.save
      render json: @alert, status: :created
    else
      render json: { errors: @alert.errors }, status: :unprocessable_entity
    end
  end

  def update
    if @alert.update(alert_params)
      render json: @alert
    else
      render json: { errors: @alert.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    @alert.destroy
    head :no_content
  end

  private

  def set_site
    @site = Site.find(params[:site_id])
  end

  def set_alert
    @alert = Alert.find(params[:id])
  end

  def alert_params
    params.require(:alert).permit(:alert_type, :email, :is_active)
  end
end
