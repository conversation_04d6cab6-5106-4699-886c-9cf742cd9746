class TestFailedJobsController < ApplicationController
  # This is a test controller to simulate the failed jobs endpoint
  # that would be implemented in monitored applications
  
  def show
    # Simulate some failed jobs data
    failed_jobs = [
      {
        id: 1,
        job_class: "EmailDeliveryJob",
        error_message: "Net::SMTPAuthenticationError: 535 Authentication failed",
        failed_at: 2.hours.ago.iso8601,
        retry_count: 3,
        queue_name: "mailers",
        arguments: {
          "user_id" => 123,
          "email_type" => "welcome",
          "template" => "user_welcome"
        },
        priority: 0,
        scheduled_at: 3.hours.ago.iso8601,
        active_job_id: "abc123-def456-ghi789"
      },
      {
        id: 2,
        job_class: "DataSyncJob",
        error_message: "ActiveRecord::RecordNotFound: Couldn't find User with 'id'=999",
        failed_at: 1.hour.ago.iso8601,
        retry_count: 1,
        queue_name: "default",
        arguments: {
          "sync_type" => "full",
          "user_ids" => [999, 1000, 1001],
          "batch_size" => 100
        },
        priority: 5,
        scheduled_at: 2.hours.ago.iso8601,
        active_job_id: "xyz789-abc123-def456"
      },
      {
        id: 3,
        job_class: "ReportGenerationJob",
        error_message: "Timeout::Error: execution expired after 300 seconds. This is a very long error message that demonstrates how the UI handles lengthy error descriptions that might contain stack traces and detailed debugging information.",
        failed_at: 30.minutes.ago.iso8601,
        retry_count: 0,
        queue_name: "reports",
        arguments: {
          "report_type" => "monthly_summary",
          "date_range" => {
            "start" => "2025-01-01",
            "end" => "2025-01-31"
          },
          "format" => "pdf",
          "recipients" => ["<EMAIL>", "<EMAIL>"]
        },
        priority: 10,
        active_job_id: "report-456-789-123"
      }
    ]

    render json: {
      failed_jobs: failed_jobs,
      total_count: failed_jobs.size,
      timestamp: Time.current.iso8601
    }
  end
end
