class AlertMailer < ApplicationMailer
  default from: "<EMAIL>"

  def downtime_alert(alert, error_message)
    @alert = alert
    @site = alert.site
    @error_message = error_message
    @timestamp = Time.current

    mail(
      to: @alert.email,
      subject: "🚨 Site Down Alert: #{@site.name}"
    )
  end

  def performance_alert(alert, health_check)
    @alert = alert
    @site = alert.site
    @health_check = health_check
    @timestamp = Time.current

    mail(
      to: @alert.email,
      subject: "⚠️ Performance Alert: #{@site.name}"
    )
  end
end
