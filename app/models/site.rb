class Site < ApplicationRecord
  has_many :health_checks, dependent: :destroy
  has_many :alerts, dependent: :destroy

  validates :name, presence: true
  validates :ip_address, presence: true, format: { with: /\A(?:[0-9]{1,3}\.){3}[0-9]{1,3}\z/, message: "must be a valid IP address" }
  validates :port, presence: true, numericality: { greater_than: 0, less_than: 65536 }
  validates :status, inclusion: { in: %w[up down unknown] }

  scope :active, -> { where(status: [ "up", "down" ]) }
  scope :up, -> { where(status: "up") }
  scope :down, -> { where(status: "down") }

  def url
    "http://#{ip_address}:#{port}/api/v1"
  end

  def health_endpoint
    "#{url}/streaming_stats"
  end

  def latest_health_check
    health_checks.order(created_at: :desc).first
  end
end
