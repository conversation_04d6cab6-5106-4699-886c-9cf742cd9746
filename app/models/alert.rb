class Alert < ApplicationRecord
  belongs_to :site

  validates :alert_type, presence: true, inclusion: { in: %w[downtime performance] }
  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }

  scope :active, -> { where(is_active: true) }
  scope :downtime_alerts, -> { where(alert_type: "downtime") }
  scope :performance_alerts, -> { where(alert_type: "performance") }

  def can_send_alert?
    return false unless is_active?
    return true if last_sent_at.nil?

    # Don't send alerts more than once every 15 minutes
    last_sent_at < 15.minutes.ago
  end

  def mark_as_sent!
    update!(last_sent_at: Time.current)
  end
end
