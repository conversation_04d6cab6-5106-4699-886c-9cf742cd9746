class HealthCheck < ApplicationRecord
  belongs_to :site

  validates :status, presence: true, inclusion: { in: %w[up down unknown] }
  validates :response_time, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :jobs_done, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :jobs_pending, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :jobs_failed, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :jobs_queued, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :visitor_count, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true

  scope :recent, -> { order(created_at: :desc) }
  scope :up, -> { where(status: "up") }
  scope :down, -> { where(status: "down") }

  def total_jobs
    jobs_done.to_i + jobs_pending.to_i + jobs_failed.to_i + jobs_queued.to_i
  end

  def success_rate
    return 0 if total_jobs == 0
    ((jobs_done.to_f / total_jobs) * 100).round(2)
  end
end
