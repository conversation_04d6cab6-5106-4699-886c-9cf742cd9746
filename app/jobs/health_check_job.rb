class HealthCheckJob < ApplicationJob
  queue_as :solid_queue

  def perform(site_id = nil)
    if site_id
      # Check a specific site
      site = Site.find_by(id: site_id)
      perform_health_check(site) if site
    else
      # Check all active sites
      Site.all.each do |site|
        perform_health_check(site)
      end
    end
  end

  private

  def perform_health_check(site)
    Rails.logger.info "Performing health check for site: #{site.name} (#{site.url})"
    HealthCheckService.new(site).perform_check
  rescue StandardError => e
    Rails.logger.error "Error performing health check for site #{site.name}: #{e.message}"
  end
end
