class HealthCheckService
  include HTT<PERSON>arty

  def initialize(site)
    @site = site
  end

  def perform_check
    start_time = Time.current

    begin
      response = HTTParty.get(@site.health_endpoint, timeout: 10)
      end_time = Time.current
      response_time = ((end_time - start_time) * 1000).round(2) # Convert to milliseconds

      if response.success?
        process_successful_response(response, response_time)
      else
        create_failed_health_check(response_time, "HTTP #{response.code}")
      end
    rescue Net::TimeoutError, Net::OpenTimeout, Net::ReadTimeout
      end_time = Time.current
      response_time = ((end_time - start_time) * 1000).round(2)
      create_failed_health_check(response_time, "Timeout")
    rescue StandardError => e
      end_time = Time.current
      response_time = ((end_time - start_time) * 1000).round(2)
      create_failed_health_check(response_time, "Connection Error: #{e.message}")
    end
  end

  private

  def process_successful_response(response, response_time)
    health_data = parse_health_data(response.body)

    health_check = @site.health_checks.create!(
      status: "up",
      response_time: response_time,
      jobs_done: health_data[:jobs_done],
      jobs_pending: health_data[:jobs_pending],
      jobs_failed: health_data[:jobs_failed],
      jobs_queued: health_data[:jobs_queued],
      last_sync_at: health_data[:last_sync_at],
      visitor_count: health_data[:visitor_count]
    )

    # Update site status
    @site.update!(
      status: "up",
      last_check_at: Time.current,
      response_time: response_time
    )

    # Check for performance alerts
    check_performance_alerts(health_check)

    health_check
  end

  def create_failed_health_check(response_time, error_message)
    health_check = @site.health_checks.create!(
      status: "down",
      response_time: response_time
    )

    # Update site status
    @site.update!(
      status: "down",
      last_check_at: Time.current,
      response_time: response_time
    )

    # Trigger downtime alerts
    trigger_downtime_alerts(error_message)

    health_check
  end

  def parse_health_data(response_body)
    begin
      data = JSON.parse(response_body)
      {
        jobs_done: data.dig("solid_queue", "jobs_done") || 0,
        jobs_pending: data.dig("solid_queue", "jobs_pending") || 0,
        jobs_failed: data.dig("solid_queue", "jobs_failed") || 0,
        jobs_queued: data.dig("solid_queue", "jobs_queued") || 0,
        last_sync_at: data["last_sync_at"] ? Time.parse(data["last_sync_at"]) : nil,
        visitor_count: data["visits_since_last_sync"] || 0
      }
    rescue JSON::ParserError
      # Return default values if JSON parsing fails
      {
        jobs_done: 0,
        jobs_pending: 0,
        jobs_failed: 0,
        jobs_queued: 0,
        last_sync_at: nil,
        visitor_count: 0
      }
    end
  end

  def check_performance_alerts(health_check)
    @site.alerts.performance_alerts.active.each do |alert|
      next unless alert.can_send_alert?

      # Trigger alert if response time is over 5 seconds
      if health_check.response_time > 5000
        AlertService.new(alert).send_performance_alert(health_check)
      end
    end
  end

  def trigger_downtime_alerts(error_message)
    @site.alerts.downtime_alerts.active.each do |alert|
      next unless alert.can_send_alert?

      AlertService.new(alert).send_downtime_alert(error_message)
    end
  end
end
