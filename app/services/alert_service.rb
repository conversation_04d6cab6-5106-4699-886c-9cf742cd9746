class AlertService
  def initialize(alert)
    @alert = alert
    @site = alert.site
  end

  def send_downtime_alert(error_message)
    AlertMailer.downtime_alert(@alert, error_message).deliver_now
    @alert.mark_as_sent!
    Rails.logger.info "Downtime alert sent to #{@alert.email} for site #{@site.name}"
  rescue StandardError => e
    Rails.logger.error "Failed to send downtime alert: #{e.message}"
  end

  def send_performance_alert(health_check)
    AlertMailer.performance_alert(@alert, health_check).deliver_now
    @alert.mark_as_sent!
    Rails.logger.info "Performance alert sent to #{@alert.email} for site #{@site.name}"
  rescue StandardError => e
    Rails.logger.error "Failed to send performance alert: #{e.message}"
  end
end
