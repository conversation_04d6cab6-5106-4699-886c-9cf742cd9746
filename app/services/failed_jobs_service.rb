class FailedJobsService
  include HTT<PERSON><PERSON>y

  def initialize(site)
    @site = site
  end

  def fetch_failed_jobs
    # For demonstration purposes, return test data if the site has failed jobs
    # if @site.latest_health_check&.jobs_failed.to_i > 0
    #   return generate_test_failed_jobs
    # end

    begin
      response = HTTParty.get("#{@site.url}/failed", timeout: 10)
      if response.success?
        parse_failed_jobs_response(response.body)
      else
        Rails.logger.error "Failed to fetch failed jobs for site #{@site.name}: HTTP #{response.code}"
        []
      end
    rescue Timeout::Error => e
      Rails.logger.error "Timeout fetching failed jobs for site #{@site.name}: #{e.message}"
      []
    rescue StandardError => e
      Rails.logger.error "Error fetching failed jobs for site #{@site.name}: #{e.message}"
      []
    end
  end

  private
  def parse_failed_jobs_response(response_body)
    begin
      data = JSON.parse(response_body)
      data.map do |job|
        {
          id: job["id"],
          job_class: job["job"]["class_name"],
          error_message: job["error"]["message"],
          failed_at: job["created_at"],
          retry_count: 0,
          queue_name: job["job"]["queue_name"],
          arguments: job["job"]["arguments"],
          priority: job["job"]["priority"],
          scheduled_at: job["job"]["scheduled_at"],
          active_job_id: job["job"]["active_job_id"]
        }
      end
    rescue JSON::ParserError => e
      Rails.logger.error "Failed to parse failed jobs response for site #{@site.name}: #{e.message}"
      []
    end
  end
end
