Date: Tue, 15 Jul 2025 10:47:39 +0200
From: <EMAIL>
To: <EMAIL>
Message-ID: <<EMAIL>>
Subject: =?UTF-8?Q?=F0=9F=9A=A8_Site_Down_Alert:_Production_App?=
Mime-Version: 1.0
Content-Type: multipart/alternative;
 boundary="--==_mimepart_687615ab6da1b_38f5ce9c9544c";
 charset=UTF-8
Content-Transfer-Encoding: 7bit


----==_mimepart_687615ab6da1b_38f5ce9c9544c
Content-Type: text/plain;
 charset=UTF-8
Content-Transfer-Encoding: 7bit

SITE DOWN ALERT - Stream Monitor
================================

Hello,

This is an automated alert from Stream Monitor.

SITE DOWN DETECTED
==================
Site: Production App
URL: http://*************:3000
Error: Connection timeout
Time: 2025-07-15 08:47:39 UTC

Please check your application immediately.

Last successful check: 2025-07-15 08:10:22 UTC

---
Stream Monitor
This is an automated message. Please do not reply.


----==_mimepart_687615ab6da1b_38f5ce9c9544c
Content-Type: text/html;
 charset=UTF-8
Content-Transfer-Encoding: quoted-printable

<!-- BEGIN app/views/layouts/mailer.html.erb --><!DOCTYPE html>
<html>
  <head>
    <meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3Dutf=
-8">
    <style>
      /* Email styles need to be inline */
    </style>
  </head>

  <body>
    <!-- BEGIN app/views/alert_mailer/downtime_alert.html.erb --><!DOCTYP=
E html>
<html>
<head>
  <meta content=3D'text/html; charset=3DUTF-8' http-equiv=3D'Content-Type=
' />
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
    }
    .header {
      background-color: #f44336;
      color: white;
      padding: 15px;
      text-align: center;
      border-radius: 5px 5px 0 0;
    }
    .content {
      padding: 20px;
      border: 1px solid #ddd;
      border-top: none;
      border-radius: 0 0 5px 5px;
    }
    .alert-details {
      background-color: #f9f9f9;
      padding: 15px;
      margin: 15px 0;
      border-left: 4px solid #f44336;
    }
    .footer {
      margin-top: 20px;
      font-size: 12px;
      color: #777;
      border-top: 1px solid #ddd;
      padding-top: 10px;
    }
  </style>
</head>
<body>
  <div class=3D"header">
    <h1>=F0=9F=9A=A8 Site Down Alert</h1>
  </div>

  <div class=3D"content">
    <p>Hello,</p>

    <p>This is an automated alert from Stream Monitor.</p>

    <div class=3D"alert-details">
      <h2>Site Down Detected</h2>
      <p><strong>Site:</strong> Production App</p>
      <p><strong>URL:</strong> http://*************:3000</p>
      <p><strong>Error:</strong> Connection timeout</p>
      <p><strong>Time:</strong> 2025-07-15 08:47:39 UTC</p>
    </div>

    <p>Please check your application immediately.</p>

    <p><strong>Last successful check:</strong> 2025-07-15 08:10:22 UTC</p=
>

    <div class=3D"footer">
      <p>Stream Monitor</p>
      <p>This is an automated message. Please do not reply.</p>
    </div>
  </div>
</body>
</html>
<!-- END app/views/alert_mailer/downtime_alert.html.erb -->
  </body>
</html>
<!-- END app/views/layouts/mailer.html.erb -->=

----==_mimepart_687615ab6da1b_38f5ce9c9544c--


Date: Tue, 15 Jul 2025 10:47:39 +0200
From: <EMAIL>
To: <EMAIL>
Message-ID: <<EMAIL>>
Subject: =?UTF-8?Q?=E2=9A=A0=EF=B8=8F_Performance_Alert:_Production_App?=
Mime-Version: 1.0
Content-Type: multipart/alternative;
 boundary="--==_mimepart_687615ab7cf16_38f5ce9c956e7";
 charset=UTF-8
Content-Transfer-Encoding: 7bit


----==_mimepart_687615ab7cf16_38f5ce9c956e7
Content-Type: text/plain;
 charset=UTF-8
Content-Transfer-Encoding: 7bit

PERFORMANCE ALERT - Stream Monitor
==================================

Hello,

This is an automated alert from Stream Monitor.

PERFORMANCE ISSUE DETECTED
==========================
Site: Production App
URL: http://*************:3000
Response Time: 6000.0ms (threshold: 5000ms)
Time: 2025-07-15 08:47:39 UTC

CURRENT METRICS
===============
Jobs Done: 50
Jobs Pending: 10
Jobs Failed: 2
Jobs Queued: 5
Visitor Count: 100
Last Sync: 2025-07-15 08:47:39 UTC

Please investigate the performance issue.

---
Stream Monitor
This is an automated message. Please do not reply.


----==_mimepart_687615ab7cf16_38f5ce9c956e7
Content-Type: text/html;
 charset=UTF-8
Content-Transfer-Encoding: 7bit

<!-- BEGIN app/views/layouts/mailer.html.erb --><!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style>
      /* Email styles need to be inline */
    </style>
  </head>

  <body>
    <!-- BEGIN app/views/alert_mailer/performance_alert.html.erb --><h1>Alert#performance_alert</h1>

<p>
  , find me in app/views/alert_mailer/performance_alert.html.erb
</p>
<!-- END app/views/alert_mailer/performance_alert.html.erb -->
  </body>
</html>
<!-- END app/views/layouts/mailer.html.erb -->
----==_mimepart_687615ab7cf16_38f5ce9c956e7--


