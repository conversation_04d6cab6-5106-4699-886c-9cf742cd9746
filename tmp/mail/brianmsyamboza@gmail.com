Date: Wed, 16 Jul 2025 10:48:30 +0200
From: <EMAIL>
To: brian<PERSON><PERSON><PERSON><PERSON>@gmail.com
Message-ID: <<EMAIL>>
Subject: =?UTF-8?Q?=F0=9F=9A=A8_Site_Down_Alert:_Local?=
Mime-Version: 1.0
Content-Type: multipart/alternative;
 boundary="--==_mimepart_6877675e15e31_554fa38cc51942";
 charset=UTF-8
Content-Transfer-Encoding: 7bit


----==_mimepart_6877675e15e31_554fa38cc51942
Content-Type: text/plain;
 charset=UTF-8
Content-Transfer-Encoding: 7bit

SITE DOWN ALERT - Stream Monitor
================================

Hello,

This is an automated alert from Stream Monitor.

SITE DOWN DETECTED
==================
Site: Local
URL: http://127.0.0.1:3002/api/v1/streaming
Error: Connection Error: Failed to open TCP connection to 127.0.0.1:3002 (Connection refused - connect(2) for "127.0.0.1" port 3002)
Time: 2025-07-16 08:48:30 UTC

Please check your application immediately.

Last successful check: 2025-07-16 08:48:30 UTC

---
Stream Monitor
This is an automated message. Please do not reply.


----==_mimepart_6877675e15e31_554fa38cc51942
Content-Type: text/html;
 charset=UTF-8
Content-Transfer-Encoding: quoted-printable

<!-- BEGIN app/views/layouts/mailer.html.erb --><!DOCTYPE html>
<html>
  <head>
    <meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3Dutf=
-8">
    <style>
      /* Email styles need to be inline */
    </style>
  </head>

  <body>
    <!-- BEGIN app/views/alert_mailer/downtime_alert.html.erb --><!DOCTYP=
E html>
<html>
<head>
  <meta content=3D'text/html; charset=3DUTF-8' http-equiv=3D'Content-Type=
' />
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
    }
    .header {
      background-color: #f44336;
      color: white;
      padding: 15px;
      text-align: center;
      border-radius: 5px 5px 0 0;
    }
    .content {
      padding: 20px;
      border: 1px solid #ddd;
      border-top: none;
      border-radius: 0 0 5px 5px;
    }
    .alert-details {
      background-color: #f9f9f9;
      padding: 15px;
      margin: 15px 0;
      border-left: 4px solid #f44336;
    }
    .footer {
      margin-top: 20px;
      font-size: 12px;
      color: #777;
      border-top: 1px solid #ddd;
      padding-top: 10px;
    }
  </style>
</head>
<body>
  <div class=3D"header">
    <h1>=F0=9F=9A=A8 Site Down Alert</h1>
  </div>

  <div class=3D"content">
    <p>Hello,</p>

    <p>This is an automated alert from Stream Monitor.</p>

    <div class=3D"alert-details">
      <h2>Site Down Detected</h2>
      <p><strong>Site:</strong> Local</p>
      <p><strong>URL:</strong> http://127.0.0.1:3002/api/v1/streaming</p>=

      <p><strong>Error:</strong> Connection Error: Failed to open TCP con=
nection to 127.0.0.1:3002 (Connection refused - connect(2) for &quot;127.=
0.0.1&quot; port 3002)</p>
      <p><strong>Time:</strong> 2025-07-16 08:48:30 UTC</p>
    </div>

    <p>Please check your application immediately.</p>

    <p><strong>Last successful check:</strong> 2025-07-16 08:48:30 UTC</p=
>

    <div class=3D"footer">
      <p>Stream Monitor</p>
      <p>This is an automated message. Please do not reply.</p>
    </div>
  </div>
</body>
</html>
<!-- END app/views/alert_mailer/downtime_alert.html.erb -->
  </body>
</html>
<!-- END app/views/layouts/mailer.html.erb -->=

----==_mimepart_6877675e15e31_554fa38cc51942--


