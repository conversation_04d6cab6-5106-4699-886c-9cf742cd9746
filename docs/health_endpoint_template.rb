# Health Check Endpoint Template for Monitored Rails Applications
#
# Add this to your monitored Rails application to provide health check data
# that the CDR Site Monitor can collect.
#
# Usage:
# 1. Add this controller to your Rails application
# 2. Add the route: get '/health', to: 'health#show'
# 3. Ensure your application has access to SolidQueue metrics
# 4. Customize the visitor_count and last_sync_at logic for your app

class HealthController < ApplicationController
  # Skip authentication for health checks
  skip_before_action :authenticate_user!, if: :devise_controller?
  skip_before_action :verify_authenticity_token

  def show
    health_data = {
      status: 'up',
      timestamp: Time.current.iso8601,
      solid_queue: solid_queue_metrics,
      last_sync_at: last_synchronization_time,
      visitor_count: visitor_count_since_last_sync,
      application: {
        name: Rails.application.class.module_parent_name,
        environment: Rails.env,
        version: app_version
      }
    }

    render json: health_data, status: :ok
  rescue StandardError => e
    Rails.logger.error "Health check failed: #{e.message}"
    render json: {
      status: 'down',
      error: e.message,
      timestamp: Time.current.iso8601
    }, status: :service_unavailable
  end

  private

  def solid_queue_metrics
    # Get SolidQueue job statistics
    {
      jobs_done: completed_jobs_count,
      jobs_pending: pending_jobs_count,
      jobs_failed: failed_jobs_count,
      jobs_queued: queued_jobs_count
    }
  rescue StandardError => e
    Rails.logger.warn "Could not fetch SolidQueue metrics: #{e.message}"
    {
      jobs_done: 0,
      jobs_pending: 0,
      jobs_failed: 0,
      jobs_queued: 0
    }
  end

  def completed_jobs_count
    # Count completed jobs since last sync
    SolidQueue::Job.finished.where(
      'finished_at > ?',
      last_synchronization_time || 1.hour.ago
    ).count
  end

  def pending_jobs_count
    SolidQueue::Job.pending.count
  end

  def failed_jobs_count
    SolidQueue::Job.failed.where(
      'created_at > ?',
      last_synchronization_time || 1.hour.ago
    ).count
  end

  def queued_jobs_count
    SolidQueue::Job.queued.count
  end

  def last_synchronization_time
    # Customize this based on your application's sync logic
    # Example: Get the last time a specific job ran
    # SyncJob.last_successful_run_time

    # Default: Use a cache key or database field
    Rails.cache.read('last_sync_time') || 1.hour.ago
  end

  def visitor_count_since_last_sync
    # Customize this based on your application's visitor tracking
    # Example implementations:

    # Option 1: Using a visits/impressions table
    # Visit.where('created_at > ?', last_synchronization_time).count

    # Option 2: Using Redis counter
    # Redis.current.get('visitor_count_since_sync').to_i

    # Option 3: Using Google Analytics API
    # GoogleAnalyticsService.new.visitor_count_since(last_synchronization_time)

    # Default: Random number for demo purposes
    rand(0..100)
  end

  def app_version
    # Return your application version
    # You can get this from:
    # - A VERSION file
    # - Git commit hash
    # - Environment variable
    # - Gem version

    if File.exist?(Rails.root.join('VERSION'))
      File.read(Rails.root.join('VERSION')).strip
    else
      'unknown'
    end
  end

  def failed_jobs
    failed_jobs_data = fetch_failed_jobs

    render json: {
      failed_jobs: failed_jobs_data,
      total_count: failed_jobs_data.size,
      timestamp: Time.current.iso8601
    }, status: :ok
  rescue StandardError => e
    Rails.logger.error "Failed jobs endpoint failed: #{e.message}"
    render json: {
      error: e.message,
      failed_jobs: [],
      total_count: 0,
      timestamp: Time.current.iso8601
    }, status: :service_unavailable
  end

  private

  def fetch_failed_jobs
    # Get failed jobs from Solid Queue
    failed_executions = SolidQueue::FailedExecution.joins(:job)
                                                   .includes(:job)
                                                   .order(created_at: :desc)
                                                   .limit(100)

    failed_executions.map do |failed_execution|
      job = failed_execution.job
      {
        id: job.id,
        job_class: job.class_name,
        error_message: failed_execution.error,
        failed_at: failed_execution.created_at.iso8601,
        retry_count: 0, # Solid Queue doesn't track retry count in the same way
        queue_name: job.queue_name,
        arguments: parse_job_arguments(job.arguments),
        priority: job.priority,
        scheduled_at: job.scheduled_at&.iso8601,
        active_job_id: job.active_job_id,
        finished_at: job.finished_at&.iso8601
      }
    end
  rescue StandardError => e
    Rails.logger.warn "Could not fetch failed jobs: #{e.message}"
    []
  end

  def parse_job_arguments(arguments_text)
    return {} if arguments_text.blank?

    begin
      # Arguments are stored as YAML in Solid Queue
      YAML.safe_load(arguments_text) || {}
    rescue StandardError
      { raw: arguments_text }
    end
  end
end

# Example routes to add to your config/routes.rb:
# get '/health', to: 'health#show'
# get '/failed_jobs', to: 'health#failed_jobs'

# Example response format:
# {
#   "status": "up",
#   "timestamp": "2024-01-15T10:30:00Z",
#   "solid_queue": {
#     "jobs_done": 150,
#     "jobs_pending": 5,
#     "jobs_failed": 2,
#     "jobs_queued": 10
#   },
#   "last_sync_at": "2024-01-15T10:25:00Z",
#   "visitor_count": 42,
#   "application": {
#     "name": "MyApp",
#     "environment": "production",
#     "version": "1.2.3"
#   }
# }
